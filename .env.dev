# 开发环境配置
ENVIRONMENT=development
HOST=0.0.0.0
PORT=8506
WORKERS=1
RELOAD=true
LOG_LEVEL=DEBUG

# 安全配置（开发环境）
ALLOWED_ORIGINS=http://localhost:8506,http://127.0.0.1:8506
API_KEYS=dev_test_key:Developer
ENABLE_API_KEY_AUTH=false

# 加密配置（开发环境使用固定密钥）
LINK_ENCRYPTION_KEY=dev_test_key_32_characters_long
SIGNATURE_SECRET=dev_signature_secret_key

# 频率限制（开发环境放宽限制）
SEARCH_RATE_LIMIT=10
HOT_MOVIES_RATE_LIMIT=50
DEFAULT_RATE_LIMIT=100
MAX_VIOLATIONS=10
BLOCK_DURATION=60

# 输入验证配置
MAX_KEYWORD_LENGTH=50
MIN_KEYWORD_LENGTH=1
MAX_CHAR_REPETITION=10

# 缓存配置
CACHE_EXPIRE_TIME=300
MAX_CACHE_SIZE=100

# 监控配置
ENABLE_PERFORMANCE_MONITORING=true
SLOW_REQUEST_THRESHOLD=2.0

# 安全增强配置（开发环境关闭部分功能）
ENABLE_LOG_SANITIZATION=false
ENABLE_GEO_BLOCKING=false
ENABLE_USER_AGENT_FILTERING=false
ENABLE_HONEYPOT=false
ENABLE_REQUEST_SIGNATURE=false
