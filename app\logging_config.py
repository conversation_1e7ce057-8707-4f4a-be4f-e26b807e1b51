"""
日志配置模块
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Dict, Any
import json

class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'client_ip'):
            log_entry['client_ip'] = record.client_ip
        
        if hasattr(record, 'user_agent'):
            log_entry['user_agent'] = record.user_agent
        
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'response_time'):
            log_entry['response_time'] = record.response_time
        
        return json.dumps(log_entry, ensure_ascii=False)

class ColoredFormatter(logging.Formatter):
    """彩色控制台日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """格式化带颜色的日志"""
        if sys.stdout.isatty():  # 只在终端中使用颜色
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            reset = self.COLORS['RESET']
            record.levelname = f"{color}{record.levelname}{reset}"
        
        return super().format(record)

def setup_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    enable_json_logging: bool = False,
    enable_file_logging: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> Dict[str, Any]:
    """
    设置应用日志配置
    
    Args:
        log_level: 日志级别
        log_dir: 日志目录
        enable_json_logging: 是否启用JSON格式日志
        enable_file_logging: 是否启用文件日志
        max_file_size: 日志文件最大大小（字节）
        backup_count: 日志文件备份数量
    
    Returns:
        Dict[str, Any]: 日志配置信息
    """
    
    # 创建日志目录
    if enable_file_logging and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    
    if enable_json_logging:
        console_formatter = JSONFormatter()
    else:
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    handlers_info = {
        'console': {
            'level': log_level,
            'formatter': 'JSON' if enable_json_logging else 'Colored',
            'enabled': True
        }
    }
    
    # 文件处理器
    if enable_file_logging:
        # 应用日志文件
        app_log_file = os.path.join(log_dir, 'app.log')
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        app_handler.setLevel(getattr(logging, log_level.upper()))
        
        if enable_json_logging:
            app_formatter = JSONFormatter()
        else:
            app_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        app_handler.setFormatter(app_formatter)
        root_logger.addHandler(app_handler)
        
        handlers_info['app_file'] = {
            'file': app_log_file,
            'level': log_level,
            'max_size': max_file_size,
            'backup_count': backup_count,
            'formatter': 'JSON' if enable_json_logging else 'Standard',
            'enabled': True
        }
        
        # 错误日志文件
        error_log_file = os.path.join(log_dir, 'error.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(app_formatter)
        root_logger.addHandler(error_handler)
        
        handlers_info['error_file'] = {
            'file': error_log_file,
            'level': 'ERROR',
            'max_size': max_file_size,
            'backup_count': backup_count,
            'formatter': 'JSON' if enable_json_logging else 'Standard',
            'enabled': True
        }
        
        # 安全日志文件
        security_log_file = os.path.join(log_dir, 'security.log')
        security_handler = logging.handlers.RotatingFileHandler(
            security_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        security_handler.setLevel(logging.WARNING)
        security_handler.setFormatter(app_formatter)
        
        # 只为安全日志器添加安全处理器
        security_logger = logging.getLogger('security')
        security_logger.addHandler(security_handler)
        security_logger.setLevel(logging.WARNING)
        
        handlers_info['security_file'] = {
            'file': security_log_file,
            'level': 'WARNING',
            'max_size': max_file_size,
            'backup_count': backup_count,
            'formatter': 'JSON' if enable_json_logging else 'Standard',
            'enabled': True
        }
    
    # 配置特定日志器
    configure_specific_loggers(log_level)
    
    return {
        'log_level': log_level,
        'log_dir': log_dir,
        'json_logging': enable_json_logging,
        'file_logging': enable_file_logging,
        'handlers': handlers_info,
        'setup_time': datetime.now().isoformat()
    }

def configure_specific_loggers(log_level: str):
    """配置特定的日志器"""
    
    # FastAPI相关日志器
    logging.getLogger("fastapi").setLevel(getattr(logging, log_level.upper()))
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)  # 减少访问日志
    
    # 第三方库日志器
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    # 应用特定日志器
    logging.getLogger("security").setLevel(logging.WARNING)
    logging.getLogger("monitoring").setLevel(logging.INFO)

class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger("requests")
    
    def log_request(self, method: str, path: str, status_code: int, 
                   response_time: float, client_ip: str, user_agent: str = "",
                   request_id: str = ""):
        """记录请求日志"""
        
        # 创建日志记录
        extra = {
            'client_ip': client_ip,
            'user_agent': user_agent,
            'request_id': request_id,
            'response_time': response_time
        }
        
        # 根据状态码选择日志级别
        if status_code < 400:
            level = logging.INFO
        elif status_code < 500:
            level = logging.WARNING
        else:
            level = logging.ERROR
        
        message = f"{method} {path} - {status_code} - {response_time:.3f}s"
        
        self.logger.log(level, message, extra=extra)
    
    def log_slow_request(self, method: str, path: str, response_time: float, 
                        threshold: float = 5.0):
        """记录慢请求"""
        if response_time > threshold:
            self.logger.warning(
                f"慢请求: {method} {path} - {response_time:.3f}s (阈值: {threshold}s)"
            )

class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger("performance")
    
    def log_performance_metric(self, metric_name: str, value: float, 
                              unit: str = "", threshold: float = None):
        """记录性能指标"""
        message = f"{metric_name}: {value}"
        if unit:
            message += f" {unit}"
        
        if threshold and value > threshold:
            self.logger.warning(f"性能告警 - {message} (阈值: {threshold})")
        else:
            self.logger.info(f"性能指标 - {message}")
    
    def log_resource_usage(self, cpu_percent: float, memory_percent: float, 
                          disk_percent: float):
        """记录资源使用情况"""
        self.logger.info(
            f"资源使用 - CPU: {cpu_percent:.1f}%, "
            f"内存: {memory_percent:.1f}%, "
            f"磁盘: {disk_percent:.1f}%"
        )
        
        # 资源告警
        if cpu_percent > 80:
            self.logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
        
        if memory_percent > 85:
            self.logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
        
        if disk_percent > 90:
            self.logger.warning(f"磁盘使用率过高: {disk_percent:.1f}%")

def get_log_stats(log_dir: str = "logs") -> Dict[str, Any]:
    """获取日志统计信息"""
    stats = {
        'log_files': [],
        'total_size': 0,
        'oldest_log': None,
        'newest_log': None
    }
    
    if not os.path.exists(log_dir):
        return stats
    
    log_files = []
    total_size = 0
    
    for filename in os.listdir(log_dir):
        if filename.endswith('.log') or '.log.' in filename:
            filepath = os.path.join(log_dir, filename)
            if os.path.isfile(filepath):
                stat = os.stat(filepath)
                file_info = {
                    'name': filename,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat()
                }
                log_files.append(file_info)
                total_size += stat.st_size
    
    # 按修改时间排序
    log_files.sort(key=lambda x: x['modified'])
    
    stats['log_files'] = log_files
    stats['total_size'] = total_size
    stats['file_count'] = len(log_files)
    
    if log_files:
        stats['oldest_log'] = log_files[0]['modified']
        stats['newest_log'] = log_files[-1]['modified']
    
    return stats

# 创建全局日志记录器实例
request_logger = RequestLogger()
performance_logger = PerformanceLogger()