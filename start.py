#!/usr/bin/env python3
"""
移动端电影搜索应用启动脚本
解决模块导入路径问题，支持从项目根目录启动
"""

import os
import sys
import uvicorn
from pathlib import Path

def main():
    """主启动函数"""
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 添加项目根目录到Python路径
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置工作目录为项目根目录
    os.chdir(project_root)
    
    # 从环境变量获取配置，提供默认值
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8506"))
    workers = int(os.getenv("WORKERS", "1"))
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    reload = os.getenv("RELOAD", "false").lower() == "true"
    
    print("=" * 60)
    print("🎬 移动端电影搜索应用")
    print("=" * 60)
    print(f"项目目录: {project_root}")
    print(f"启动配置: host={host}, port={port}, workers={workers}")
    print(f"日志级别: {log_level}, 热重载: {reload}")
    print("=" * 60)
    
    # 启动应用
    try:
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level,
            workers=workers if not reload else 1,  # reload模式下只能使用1个worker
            # 生产环境优化配置
            access_log=True,
            use_colors=True,
            reload_excludes=["logs/*", "*.log", "__pycache__/*", "*.pyc"] if reload else None
        )
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
