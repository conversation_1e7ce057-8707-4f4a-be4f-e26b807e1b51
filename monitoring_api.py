"""
监控API端点
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse
from typing import Optional
import time
from datetime import datetime
from monitoring import metrics_collector
from logging_config import access_logger, performance_logger

# 创建监控路由
monitoring_router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])

@monitoring_router.get("/health")
async def get_health_status():
    """获取应用健康状态"""
    try:
        health_status = metrics_collector.get_health_status()
        return JSONResponse(content=health_status)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")

@monitoring_router.get("/stats")
async def get_stats(time_window: int = Query(3600, description="时间窗口（秒）", ge=60, le=86400)):
    """获取统计信息"""
    try:
        stats = {
            "timestamp": datetime.now().isoformat(),
            "time_window": time_window,
            "request_stats": metrics_collector.get_request_stats(time_window),
            "system_stats": metrics_collector.get_system_stats() if hasattr(metrics_collector, 'get_system_stats') else {},
            "uptime": int(time.time() - metrics_collector.stats['uptime_start'])
        }
        return JSONResponse(content=stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@monitoring_router.get("/metrics")
async def get_metrics():
    """获取Prometheus格式的指标"""
    try:
        request_stats = metrics_collector.get_request_stats()
        
        # 生成Prometheus格式的指标
        metrics_text = f"""# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total {metrics_collector.stats['total_requests']}

# HELP http_request_duration_seconds HTTP request duration in seconds
# TYPE http_request_duration_seconds histogram
http_request_duration_seconds_sum {sum(metrics_collector.response_times)}
http_request_duration_seconds_count {len(metrics_collector.response_times)}

# HELP http_requests_error_rate HTTP request error rate
# TYPE http_requests_error_rate gauge
http_requests_error_rate {request_stats['error_rate'] / 100}

# HELP app_uptime_seconds Application uptime in seconds
# TYPE app_uptime_seconds counter
app_uptime_seconds {int(time.time() - metrics_collector.stats['uptime_start'])}
"""
        
        return JSONResponse(
            content={"metrics": metrics_text},
            headers={"Content-Type": "text/plain"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")

@monitoring_router.post("/reset")
async def reset_metrics():
    """重置监控指标（仅开发环境）"""
    try:
        metrics_collector.reset_stats()
        return {"message": "监控指标已重置", "timestamp": datetime.now().isoformat()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置指标失败: {str(e)}")

@monitoring_router.get("/alerts")
async def get_alerts():
    """获取告警信息"""
    try:
        health_status = metrics_collector.get_health_status()
        request_stats = metrics_collector.get_request_stats(300)  # 最近5分钟
        
        alerts = []
        
        # 检查各种告警条件
        if request_stats['error_rate'] > 5:
            alerts.append({
                "level": "warning" if request_stats['error_rate'] < 10 else "critical",
                "type": "high_error_rate",
                "message": f"错误率过高: {request_stats['error_rate']}%",
                "value": request_stats['error_rate'],
                "threshold": 5,
                "timestamp": datetime.now().isoformat()
            })
        
        if request_stats['avg_response_time'] > 3:
            alerts.append({
                "level": "warning" if request_stats['avg_response_time'] < 5 else "critical",
                "type": "slow_response",
                "message": f"响应时间过长: {request_stats['avg_response_time']}s",
                "value": request_stats['avg_response_time'],
                "threshold": 3,
                "timestamp": datetime.now().isoformat()
            })
        
        if request_stats['requests_per_minute'] > 100:
            alerts.append({
                "level": "info",
                "type": "high_traffic",
                "message": f"高流量: {request_stats['requests_per_minute']} 请求/分钟",
                "value": request_stats['requests_per_minute'],
                "threshold": 100,
                "timestamp": datetime.now().isoformat()
            })
        
        return {
            "alerts": alerts,
            "alert_count": len(alerts),
            "last_check": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警信息失败: {str(e)}")

@monitoring_router.get("/logs/recent")
async def get_recent_logs(
    level: Optional[str] = Query(None, description="日志级别过滤"),
    limit: int = Query(100, description="返回条数", ge=1, le=1000)
):
    """获取最近的日志（简化版本）"""
    try:
        # 这里应该读取日志文件，简化版本返回基本信息
        logs = []
        
        # 从请求历史中生成日志样例
        recent_requests = list(metrics_collector.request_history)[-limit:]
        
        for req in recent_requests:
            log_level = "ERROR" if req.status_code >= 400 else "INFO"
            if level and log_level != level.upper():
                continue
                
            logs.append({
                "timestamp": datetime.fromtimestamp(req.timestamp).isoformat(),
                "level": log_level,
                "message": f"{req.method} {req.path} - {req.status_code} - {req.response_time:.3f}s",
                "details": {
                    "method": req.method,
                    "path": req.path,
                    "status_code": req.status_code,
                    "response_time": req.response_time,
                    "client_id": req.client_id
                }
            })
        
        return {
            "logs": logs[-limit:],
            "count": len(logs),
            "last_update": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")