"""
会话管理器
"""

import time
import hashlib
import secrets
import threading
from typing import Dict, <PERSON><PERSON>, Tuple
from log_sanitizer import get_sanitized_logger

logger = get_sanitized_logger(__name__)

class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.sessions: Dict[str, dict] = {}
        self.fingerprint_sessions: Dict[str, str] = {}  # 指纹 -> 令牌映射
        self.lock = threading.Lock()
        
        # 会话配置
        self.session_timeout = 1800  # 30分钟
        self.max_sessions_per_fingerprint = 3  # 每个指纹最多3个会话
        self.cleanup_interval = 300  # 5分钟清理一次
        
        # 启动清理线程
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """启动会话清理线程"""
        def cleanup_expired_sessions():
            while True:
                try:
                    self.cleanup_expired_sessions()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    logger.error(f"会话清理异常: {e}")
                    time.sleep(60)
        
        cleanup_thread = threading.Thread(target=cleanup_expired_sessions, daemon=True)
        cleanup_thread.start()
        logger.info("会话清理线程已启动")
    
    def _generate_token(self) -> str:
        """生成会话令牌"""
        return secrets.token_urlsafe(32)
    
    def _hash_fingerprint(self, fingerprint: str) -> str:
        """对指纹进行哈希处理"""
        return hashlib.sha256(fingerprint.encode()).hexdigest()[:16]
    
    def create_session(self, fingerprint: str, client_ip: str) -> Tuple[str, int]:
        """
        创建会话
        
        Args:
            fingerprint: 浏览器指纹
            client_ip: 客户端IP
            
        Returns:
            tuple[str, int]: (会话令牌, 过期时间秒数)
        """
        with self.lock:
            # 哈希指纹以保护隐私
            hashed_fingerprint = self._hash_fingerprint(fingerprint)
            
            # 检查该指纹的现有会话数量
            existing_sessions = [
                token for token, session in self.sessions.items()
                if session.get('fingerprint_hash') == hashed_fingerprint
            ]
            
            # 如果超过限制，删除最旧的会话
            if len(existing_sessions) >= self.max_sessions_per_fingerprint:
                oldest_token = min(existing_sessions, 
                                 key=lambda t: self.sessions[t]['created_at'])
                self._remove_session(oldest_token)
                logger.warning(f"指纹 {hashed_fingerprint} 会话数量超限，删除最旧会话")
            
            # 生成新会话
            token = self._generate_token()
            expires_at = time.time() + self.session_timeout
            
            session_data = {
                'token': token,
                'fingerprint_hash': hashed_fingerprint,
                'client_ip': client_ip,
                'created_at': time.time(),
                'expires_at': expires_at,
                'request_count': 0,
                'last_request_time': time.time()
            }
            
            self.sessions[token] = session_data
            self.fingerprint_sessions[hashed_fingerprint] = token
            
            logger.info(f"创建会话: 指纹 {hashed_fingerprint} from IP {client_ip}")
            
            return token, self.session_timeout
    
    def validate_session(self, token: str, fingerprint: str, client_ip: str) -> Tuple[bool, str]:
        """
        验证会话
        
        Args:
            token: 会话令牌
            fingerprint: 浏览器指纹
            client_ip: 客户端IP
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        with self.lock:
            if not token:
                return False, "缺少会话令牌"
            
            session = self.sessions.get(token)
            if not session:
                return False, "无效的会话令牌"
            
            # 检查是否过期
            if time.time() > session['expires_at']:
                self._remove_session(token)
                return False, "会话已过期"
            
            # 验证指纹
            hashed_fingerprint = self._hash_fingerprint(fingerprint)
            if session['fingerprint_hash'] != hashed_fingerprint:
                logger.warning(f"指纹不匹配: 期望 {session['fingerprint_hash']}, 实际 {hashed_fingerprint}")
                return False, "浏览器指纹验证失败"
            
            # 验证IP（可选，允许一定的IP变化）
            if session['client_ip'] != client_ip:
                logger.info(f"IP变化: {session['client_ip']} -> {client_ip}")
                # 更新IP但不拒绝请求
                session['client_ip'] = client_ip
            
            # 更新会话活动时间
            session['last_request_time'] = time.time()
            session['request_count'] += 1
            
            return True, ""
    
    def _remove_session(self, token: str):
        """移除会话（内部方法，需要持有锁）"""
        session = self.sessions.get(token)
        if session:
            fingerprint_hash = session['fingerprint_hash']
            
            # 从会话字典中移除
            del self.sessions[token]
            
            # 从指纹映射中移除（如果是当前令牌）
            if self.fingerprint_sessions.get(fingerprint_hash) == token:
                del self.fingerprint_sessions[fingerprint_hash]
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        with self.lock:
            current_time = time.time()
            expired_tokens = [
                token for token, session in self.sessions.items()
                if current_time > session['expires_at']
            ]
            
            for token in expired_tokens:
                self._remove_session(token)
            
            if expired_tokens:
                logger.info(f"清理了 {len(expired_tokens)} 个过期会话")
    
    def get_session_stats(self) -> dict:
        """获取会话统计信息"""
        with self.lock:
            active_sessions = len(self.sessions)
            unique_fingerprints = len(set(
                session['fingerprint_hash'] for session in self.sessions.values()
            ))
            
            return {
                'active_sessions': active_sessions,
                'unique_fingerprints': unique_fingerprints,
                'total_requests': sum(
                    session['request_count'] for session in self.sessions.values()
                )
            }
    
    def is_suspicious_activity(self, fingerprint: str) -> bool:
        """检测可疑活动"""
        hashed_fingerprint = self._hash_fingerprint(fingerprint)
        
        with self.lock:
            # 检查该指纹的请求频率
            sessions_for_fingerprint = [
                session for session in self.sessions.values()
                if session['fingerprint_hash'] == hashed_fingerprint
            ]
            
            if not sessions_for_fingerprint:
                return False
            
            # 计算最近5分钟的请求数
            recent_requests = sum(
                session['request_count'] for session in sessions_for_fingerprint
                if time.time() - session['last_request_time'] < 300
            )
            
            # 如果5分钟内请求超过20次，认为可疑
            return recent_requests > 20

# 创建全局会话管理器实例
session_manager = SessionManager()
