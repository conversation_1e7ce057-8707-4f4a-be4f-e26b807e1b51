#!/usr/bin/env python3
"""
日志管理脚本 - 日志轮转、清理和分析
"""

import os
import gzip
import shutil
import logging
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import argparse

class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 配置
        self.max_log_age_days = 30  # 日志保留天数
        self.max_log_size_mb = 100  # 单个日志文件最大大小
        self.compress_after_days = 7  # 多少天后压缩日志
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def rotate_logs(self):
        """轮转日志文件"""
        self.logger.info("开始日志轮转...")
        
        log_files = [
            'app.log', 'error.log', 'security.log', 
            'access.log', 'performance.log'
        ]
        
        for log_file in log_files:
            log_path = self.log_dir / log_file
            if not log_path.exists():
                continue
            
            # 检查文件大小
            file_size_mb = log_path.stat().st_size / (1024 * 1024)
            if file_size_mb > self.max_log_size_mb:
                self._rotate_single_file(log_path)
    
    def _rotate_single_file(self, log_path: Path):
        """轮转单个日志文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        rotated_name = f"{log_path.stem}_{timestamp}.log"
        rotated_path = log_path.parent / rotated_name
        
        try:
            shutil.move(str(log_path), str(rotated_path))
            self.logger.info(f"日志文件已轮转: {log_path.name} -> {rotated_name}")
            
            # 创建新的空日志文件
            log_path.touch()
            
        except Exception as e:
            self.logger.error(f"轮转日志文件失败 {log_path}: {e}")
    
    def compress_old_logs(self):
        """压缩旧日志文件"""
        self.logger.info("开始压缩旧日志...")
        
        cutoff_date = datetime.now() - timedelta(days=self.compress_after_days)
        
        for log_file in self.log_dir.glob("*.log"):
            if log_file.name in ['app.log', 'error.log', 'security.log', 'access.log', 'performance.log']:
                continue  # 跳过当前活跃的日志文件
            
            # 检查文件修改时间
            file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            if file_mtime < cutoff_date:
                self._compress_file(log_file)
    
    def _compress_file(self, file_path: Path):
        """压缩单个文件"""
        compressed_path = file_path.with_suffix('.log.gz')
        
        try:
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除原文件
            file_path.unlink()
            self.logger.info(f"日志文件已压缩: {file_path.name} -> {compressed_path.name}")
            
        except Exception as e:
            self.logger.error(f"压缩日志文件失败 {file_path}: {e}")
    
    def cleanup_old_logs(self):
        """清理过期日志"""
        self.logger.info("开始清理过期日志...")
        
        cutoff_date = datetime.now() - timedelta(days=self.max_log_age_days)
        
        # 清理压缩日志
        for gz_file in self.log_dir.glob("*.gz"):
            file_mtime = datetime.fromtimestamp(gz_file.stat().st_mtime)
            if file_mtime < cutoff_date:
                try:
                    gz_file.unlink()
                    self.logger.info(f"已删除过期日志: {gz_file.name}")
                except Exception as e:
                    self.logger.error(f"删除过期日志失败 {gz_file}: {e}")
        
        # 清理未压缩的旧日志
        for log_file in self.log_dir.glob("*_*.log"):
            file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            if file_mtime < cutoff_date:
                try:
                    log_file.unlink()
                    self.logger.info(f"已删除过期日志: {log_file.name}")
                except Exception as e:
                    self.logger.error(f"删除过期日志失败 {log_file}: {e}")
    
    def analyze_logs(self, log_type: str = "access", hours: int = 24) -> Dict[str, Any]:
        """分析日志"""
        self.logger.info(f"开始分析 {log_type} 日志，时间范围: {hours} 小时")
        
        log_file = self.log_dir / f"{log_type}.log"
        if not log_file.exists():
            return {"error": f"日志文件不存在: {log_file}"}
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        if log_type == "access":
            return self._analyze_access_logs(log_file, cutoff_time)
        elif log_type == "security":
            return self._analyze_security_logs(log_file, cutoff_time)
        elif log_type == "performance":
            return self._analyze_performance_logs(log_file, cutoff_time)
        else:
            return self._analyze_general_logs(log_file, cutoff_time)
    
    def _analyze_access_logs(self, log_file: Path, cutoff_time: datetime) -> Dict[str, Any]:
        """分析访问日志"""
        stats = {
            'total_requests': 0,
            'status_codes': {},
            'top_ips': {},
            'top_paths': {},
            'top_user_agents': {},
            'error_requests': 0,
            'avg_response_time': 0,
            'response_times': []
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue
                    
                    # 解析访问日志行
                    parts = line.strip().split(' - ')
                    if len(parts) < 2:
                        continue
                    
                    # 提取时间戳
                    timestamp_str = parts[0]
                    try:
                        log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        if log_time < cutoff_time:
                            continue
                    except ValueError:
                        continue
                    
                    stats['total_requests'] += 1
                    
                    # 解析请求信息
                    request_info = parts[1] if len(parts) > 1 else ""
                    
                    # 提取IP地址
                    ip_match = request_info.split(' ')[0] if request_info else "unknown"
                    stats['top_ips'][ip_match] = stats['top_ips'].get(ip_match, 0) + 1
                    
                    # 提取状态码和响应时间（简化解析）
                    if '"' in request_info:
                        parts_detail = request_info.split('"')
                        if len(parts_detail) >= 3:
                            after_request = parts_detail[2].strip().split()
                            if len(after_request) >= 3:
                                try:
                                    status_code = int(after_request[0])
                                    response_time = float(after_request[2].replace('s', ''))
                                    
                                    stats['status_codes'][status_code] = stats['status_codes'].get(status_code, 0) + 1
                                    stats['response_times'].append(response_time)
                                    
                                    if status_code >= 400:
                                        stats['error_requests'] += 1
                                except (ValueError, IndexError):
                                    pass
        
        except Exception as e:
            self.logger.error(f"分析访问日志失败: {e}")
            return {"error": str(e)}
        
        # 计算平均响应时间
        if stats['response_times']:
            stats['avg_response_time'] = sum(stats['response_times']) / len(stats['response_times'])
        
        # 排序统计
        stats['top_ips'] = dict(sorted(stats['top_ips'].items(), key=lambda x: x[1], reverse=True)[:10])
        stats['status_codes'] = dict(sorted(stats['status_codes'].items()))
        
        # 计算错误率
        stats['error_rate'] = (stats['error_requests'] / stats['total_requests'] * 100) if stats['total_requests'] > 0 else 0
        
        return stats
    
    def _analyze_security_logs(self, log_file: Path, cutoff_time: datetime) -> Dict[str, Any]:
        """分析安全日志"""
        stats = {
            'total_events': 0,
            'attack_types': {},
            'blocked_ips': set(),
            'top_attackers': {},
            'severity_levels': {}
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue
                    
                    try:
                        # 解析JSON格式的安全日志
                        log_entry = json.loads(line.strip())
                        
                        # 检查时间戳
                        log_time = datetime.fromisoformat(log_entry.get('timestamp', ''))
                        if log_time < cutoff_time:
                            continue
                        
                        stats['total_events'] += 1
                        
                        # 统计事件类型
                        event_type = log_entry.get('event_type', 'unknown')
                        stats['attack_types'][event_type] = stats['attack_types'].get(event_type, 0) + 1
                        
                        # 统计严重程度
                        severity = log_entry.get('severity', 'INFO')
                        stats['severity_levels'][severity] = stats['severity_levels'].get(severity, 0) + 1
                        
                        # 统计攻击者IP
                        client_info = log_entry.get('client', {})
                        if isinstance(client_info, dict):
                            client_ip = client_info.get('ip', 'unknown')
                            stats['top_attackers'][client_ip] = stats['top_attackers'].get(client_ip, 0) + 1
                            
                            # 检查是否被阻止
                            details = log_entry.get('details', {})
                            if isinstance(details, dict) and details.get('blocked'):
                                stats['blocked_ips'].add(client_ip)
                    
                    except (json.JSONDecodeError, ValueError, KeyError):
                        continue
        
        except Exception as e:
            self.logger.error(f"分析安全日志失败: {e}")
            return {"error": str(e)}
        
        # 转换集合为列表
        stats['blocked_ips'] = list(stats['blocked_ips'])
        
        # 排序统计
        stats['attack_types'] = dict(sorted(stats['attack_types'].items(), key=lambda x: x[1], reverse=True))
        stats['top_attackers'] = dict(sorted(stats['top_attackers'].items(), key=lambda x: x[1], reverse=True)[:10])
        
        return stats
    
    def _analyze_performance_logs(self, log_file: Path, cutoff_time: datetime) -> Dict[str, Any]:
        """分析性能日志"""
        stats = {
            'total_events': 0,
            'slow_requests': 0,
            'high_memory_events': 0,
            'high_cpu_events': 0,
            'avg_response_times': {},
            'slowest_endpoints': {}
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue
                    
                    try:
                        # 解析JSON格式的性能日志
                        log_entry = json.loads(line.strip())
                        
                        # 检查时间戳
                        log_time = datetime.fromisoformat(log_entry.get('timestamp', ''))
                        if log_time < cutoff_time:
                            continue
                        
                        stats['total_events'] += 1
                        
                        # 统计事件类型
                        event_type = log_entry.get('event_type', 'unknown')
                        
                        if event_type == 'slow_request':
                            stats['slow_requests'] += 1
                            
                            # 统计慢请求端点
                            method = log_entry.get('method', 'GET')
                            path = log_entry.get('path', '/')
                            endpoint = f"{method} {path}"
                            response_time = log_entry.get('response_time', 0)
                            
                            if endpoint not in stats['slowest_endpoints']:
                                stats['slowest_endpoints'][endpoint] = []
                            stats['slowest_endpoints'][endpoint].append(response_time)
                        
                        elif event_type == 'high_memory_usage':
                            stats['high_memory_events'] += 1
                        
                        elif event_type == 'high_cpu_usage':
                            stats['high_cpu_events'] += 1
                    
                    except (json.JSONDecodeError, ValueError, KeyError):
                        continue
        
        except Exception as e:
            self.logger.error(f"分析性能日志失败: {e}")
            return {"error": str(e)}
        
        # 计算平均响应时间
        for endpoint, times in stats['slowest_endpoints'].items():
            stats['avg_response_times'][endpoint] = sum(times) / len(times)
        
        # 排序最慢端点
        stats['slowest_endpoints'] = dict(
            sorted(stats['avg_response_times'].items(), key=lambda x: x[1], reverse=True)[:10]
        )
        
        return stats
    
    def _analyze_general_logs(self, log_file: Path, cutoff_time: datetime) -> Dict[str, Any]:
        """分析一般日志"""
        stats = {
            'total_lines': 0,
            'log_levels': {},
            'error_messages': {},
            'warning_messages': {}
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.strip():
                        continue
                    
                    stats['total_lines'] += 1
                    
                    # 简单解析日志级别
                    if ' - ERROR - ' in line:
                        stats['log_levels']['ERROR'] = stats['log_levels'].get('ERROR', 0) + 1
                        # 提取错误消息
                        error_msg = line.split(' - ERROR - ')[1].split('\n')[0][:100]
                        stats['error_messages'][error_msg] = stats['error_messages'].get(error_msg, 0) + 1
                    
                    elif ' - WARNING - ' in line:
                        stats['log_levels']['WARNING'] = stats['log_levels'].get('WARNING', 0) + 1
                        # 提取警告消息
                        warning_msg = line.split(' - WARNING - ')[1].split('\n')[0][:100]
                        stats['warning_messages'][warning_msg] = stats['warning_messages'].get(warning_msg, 0) + 1
                    
                    elif ' - INFO - ' in line:
                        stats['log_levels']['INFO'] = stats['log_levels'].get('INFO', 0) + 1
                    
                    elif ' - DEBUG - ' in line:
                        stats['log_levels']['DEBUG'] = stats['log_levels'].get('DEBUG', 0) + 1
        
        except Exception as e:
            self.logger.error(f"分析一般日志失败: {e}")
            return {"error": str(e)}
        
        # 排序错误和警告消息
        stats['error_messages'] = dict(sorted(stats['error_messages'].items(), key=lambda x: x[1], reverse=True)[:10])
        stats['warning_messages'] = dict(sorted(stats['warning_messages'].items(), key=lambda x: x[1], reverse=True)[:10])
        
        return stats
    
    def generate_report(self, output_file: str = None) -> str:
        """生成日志分析报告"""
        self.logger.info("生成日志分析报告...")
        
        report = {
            'generated_at': datetime.now().isoformat(),
            'log_directory': str(self.log_dir),
            'analysis': {}
        }
        
        # 分析各种日志
        log_types = ['access', 'security', 'performance', 'error']
        for log_type in log_types:
            report['analysis'][log_type] = self.analyze_logs(log_type, hours=24)
        
        # 生成报告文本
        report_text = self._format_report(report)
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                self.logger.info(f"报告已保存到: {output_file}")
            except Exception as e:
                self.logger.error(f"保存报告失败: {e}")
        
        return report_text
    
    def _format_report(self, report: Dict[str, Any]) -> str:
        """格式化报告"""
        lines = []
        lines.append("=" * 60)
        lines.append("日志分析报告")
        lines.append("=" * 60)
        lines.append(f"生成时间: {report['generated_at']}")
        lines.append(f"日志目录: {report['log_directory']}")
        lines.append("")
        
        for log_type, analysis in report['analysis'].items():
            if 'error' in analysis:
                lines.append(f"{log_type.upper()} 日志: 分析失败 - {analysis['error']}")
                lines.append("")
                continue
            
            lines.append(f"{log_type.upper()} 日志分析:")
            lines.append("-" * 40)
            
            if log_type == 'access':
                lines.append(f"总请求数: {analysis.get('total_requests', 0)}")
                lines.append(f"错误请求数: {analysis.get('error_requests', 0)}")
                lines.append(f"错误率: {analysis.get('error_rate', 0):.2f}%")
                lines.append(f"平均响应时间: {analysis.get('avg_response_time', 0):.3f}s")
                
                if analysis.get('top_ips'):
                    lines.append("热门IP地址:")
                    for ip, count in list(analysis['top_ips'].items())[:5]:
                        lines.append(f"  {ip}: {count} 次")
                
                if analysis.get('status_codes'):
                    lines.append("状态码分布:")
                    for code, count in analysis['status_codes'].items():
                        lines.append(f"  {code}: {count} 次")
            
            elif log_type == 'security':
                lines.append(f"安全事件总数: {analysis.get('total_events', 0)}")
                lines.append(f"被阻止IP数量: {len(analysis.get('blocked_ips', []))}")
                
                if analysis.get('attack_types'):
                    lines.append("攻击类型分布:")
                    for attack_type, count in list(analysis['attack_types'].items())[:5]:
                        lines.append(f"  {attack_type}: {count} 次")
                
                if analysis.get('top_attackers'):
                    lines.append("主要攻击者:")
                    for ip, count in list(analysis['top_attackers'].items())[:5]:
                        lines.append(f"  {ip}: {count} 次")
            
            elif log_type == 'performance':
                lines.append(f"性能事件总数: {analysis.get('total_events', 0)}")
                lines.append(f"慢请求数量: {analysis.get('slow_requests', 0)}")
                lines.append(f"高内存使用事件: {analysis.get('high_memory_events', 0)}")
                lines.append(f"高CPU使用事件: {analysis.get('high_cpu_events', 0)}")
                
                if analysis.get('slowest_endpoints'):
                    lines.append("最慢端点:")
                    for endpoint, avg_time in list(analysis['slowest_endpoints'].items())[:5]:
                        lines.append(f"  {endpoint}: {avg_time:.3f}s")
            
            lines.append("")
        
        lines.append("=" * 60)
        return "\n".join(lines)
    
    def run_maintenance(self):
        """运行日志维护任务"""
        self.logger.info("开始日志维护任务...")
        
        try:
            self.rotate_logs()
            self.compress_old_logs()
            self.cleanup_old_logs()
            self.logger.info("日志维护任务完成")
        except Exception as e:
            self.logger.error(f"日志维护任务失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="日志管理工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录")
    parser.add_argument("--action", choices=['rotate', 'compress', 'cleanup', 'analyze', 'report', 'maintenance'], 
                       default='maintenance', help="执行的操作")
    parser.add_argument("--log-type", default="access", help="分析的日志类型")
    parser.add_argument("--hours", type=int, default=24, help="分析时间范围（小时）")
    parser.add_argument("--output", help="报告输出文件")
    
    args = parser.parse_args()
    
    log_manager = LogManager(args.log_dir)
    
    if args.action == 'rotate':
        log_manager.rotate_logs()
    elif args.action == 'compress':
        log_manager.compress_old_logs()
    elif args.action == 'cleanup':
        log_manager.cleanup_old_logs()
    elif args.action == 'analyze':
        result = log_manager.analyze_logs(args.log_type, args.hours)
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif args.action == 'report':
        report = log_manager.generate_report(args.output)
        if not args.output:
            print(report)
    elif args.action == 'maintenance':
        log_manager.run_maintenance()

if __name__ == "__main__":
    main()