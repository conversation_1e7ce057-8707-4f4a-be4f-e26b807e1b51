#!/usr/bin/env python3
"""
查找同时有百度和夸克链接的内容
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from modules.wps_searcher import WPSContentSearcher

def find_dual_links():
    """查找同时有百度和夸克链接的内容"""
    searcher = WPSContentSearcher()
    
    print("=== 查找同时有百度和夸克链接的内容 ===")
    
    # 获取第一个文档的数据
    url = searcher.document_urls[0]
    document_data = searcher._get_document_data(url)
    
    if not document_data:
        print("❌ 无法获取文档数据")
        return
    
    dual_link_items = []
    
    for logic_block in document_data.get('content', []):
        if logic_block.get('type') == 'logic_block':
            block_content = logic_block.get('content', [])
            
            for i, block_tile in enumerate(block_content):
                if block_tile.get('type') == 'block_tile':
                    tile_content = block_tile.get('content', [])
                    texts = searcher._extract_text_from_content(tile_content)
                    tile_text = ' '.join(texts).replace("-", "")
                    
                    if not tile_text.strip():
                        continue
                    
                    # 收集当前tile和后续tile的所有链接
                    all_links = []
                    
                    # 当前tile的链接
                    tile_links = searcher._extract_links_from_content(tile_content)
                    all_links.extend(tile_links)
                    
                    # 后续tile的链接
                    for j in range(i + 1, min(i + 5, len(block_content))):
                        next_tile = block_content[j]
                        if next_tile.get('type') == 'block_tile':
                            next_content = next_tile.get('content', [])
                            next_links = searcher._extract_links_from_content(next_content)
                            
                            if next_links:
                                all_links.extend(next_links)
                                break
                    
                    # 检查是否同时有百度和夸克链接
                    baidu_links = [link for link in all_links if link['type'] == 'baidu']
                    quark_links = [link for link in all_links if link['type'] == 'quark']
                    
                    if baidu_links and quark_links:
                        dual_link_items.append({
                            'title': tile_text[:100],
                            'baidu_count': len(baidu_links),
                            'quark_count': len(quark_links),
                            'baidu_links': [link['url'] for link in baidu_links],
                            'quark_links': [link['url'] for link in quark_links]
                        })
                        
                        if len(dual_link_items) >= 10:  # 只收集前10个
                            break
            
            if len(dual_link_items) >= 10:
                break
    
    print(f"找到同时有百度和夸克链接的内容项: {len(dual_link_items)}")
    
    for i, item in enumerate(dual_link_items[:5]):
        print(f"\n--- 内容项 {i+1} ---")
        print(f"标题: {item['title']}")
        print(f"百度链接数量: {item['baidu_count']}")
        print(f"夸克链接数量: {item['quark_count']}")
        
        print("百度链接:")
        for link in item['baidu_links'][:2]:
            print(f"  {link}")
        
        print("夸克链接:")
        for link in item['quark_links'][:2]:
            print(f"  {link}")
    
    # 如果找到了双链接内容，测试搜索其中一个
    if dual_link_items:
        test_item = dual_link_items[0]
        # 提取标题中的关键词进行搜索测试
        title_words = test_item['title'].split()
        for word in title_words:
            if len(word) >= 2 and word.isalnum():
                print(f"\n=== 测试搜索关键词: {word} ===")
                results = searcher.search(word)
                
                for result in results:
                    if result.quark_links:
                        print(f"✅ 找到有夸克链接的结果: {result.title}")
                        print(f"  夸克链接数量: {len(result.quark_links)}")
                        for link in result.quark_links[:2]:
                            print(f"    {link}")
                        return
                
                break

if __name__ == "__main__":
    find_dual_links()