#!/usr/bin/env python3
"""
性能监控脚本 - 独立运行的监控工具
"""

import requests
import time
import json
import sys
from datetime import datetime
from typing import Dict, Any
import threading
import signal

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, base_url: str = "http://localhost:8506", interval: int = 30):
        self.base_url = base_url
        self.interval = interval
        self.running = True
        self.session = requests.Session()
        self.alerts = []
        
        # 阈值配置
        self.thresholds = {
            'error_rate': 5.0,          # 错误率阈值 5%
            'response_time': 3.0,       # 响应时间阈值 3秒
            'requests_per_minute': 200, # 请求频率阈值 200/分钟
            'cpu_usage': 80.0,          # CPU使用率阈值 80%
            'memory_usage': 85.0        # 内存使用率阈值 85%
        }
    
    def check_health(self) -> Dict[str, Any]:
        """检查应用健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/monitoring/health", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "unhealthy", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"status": "unreachable", "error": str(e)}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            response = self.session.get(f"{self.base_url}/api/monitoring/stats", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"error": str(e)}
    
    def get_alerts(self) -> Dict[str, Any]:
        """获取告警信息"""
        try:
            response = self.session.get(f"{self.base_url}/api/monitoring/alerts", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"alerts": [], "error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"alerts": [], "error": str(e)}
    
    def check_endpoint_availability(self) -> Dict[str, bool]:
        """检查关键端点可用性"""
        endpoints = [
            "/",
            "/health",
            "/api/hot-movies",
        ]
        
        results = {}
        for endpoint in endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=5)
                results[endpoint] = response.status_code < 400
            except:
                results[endpoint] = False
        
        return results
    
    def analyze_performance(self, stats: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析性能数据并生成告警"""
        alerts = []
        
        if "request_stats" in stats:
            req_stats = stats["request_stats"]
            
            # 检查错误率
            if req_stats.get("error_rate", 0) > self.thresholds["error_rate"]:
                alerts.append({
                    "type": "high_error_rate",
                    "level": "warning",
                    "message": f"错误率过高: {req_stats['error_rate']}%",
                    "value": req_stats["error_rate"],
                    "threshold": self.thresholds["error_rate"]
                })
            
            # 检查响应时间
            if req_stats.get("avg_response_time", 0) > self.thresholds["response_time"]:
                alerts.append({
                    "type": "slow_response",
                    "level": "warning",
                    "message": f"平均响应时间过长: {req_stats['avg_response_time']}s",
                    "value": req_stats["avg_response_time"],
                    "threshold": self.thresholds["response_time"]
                })
            
            # 检查请求频率
            if req_stats.get("requests_per_minute", 0) > self.thresholds["requests_per_minute"]:
                alerts.append({
                    "type": "high_traffic",
                    "level": "info",
                    "message": f"高流量: {req_stats['requests_per_minute']} 请求/分钟",
                    "value": req_stats["requests_per_minute"],
                    "threshold": self.thresholds["requests_per_minute"]
                })
        
        if "system_stats" in stats:
            sys_stats = stats["system_stats"]
            
            # 检查CPU使用率
            if sys_stats.get("cpu_percent", 0) > self.thresholds["cpu_usage"]:
                alerts.append({
                    "type": "high_cpu",
                    "level": "warning",
                    "message": f"CPU使用率过高: {sys_stats['cpu_percent']}%",
                    "value": sys_stats["cpu_percent"],
                    "threshold": self.thresholds["cpu_usage"]
                })
            
            # 检查内存使用率
            if sys_stats.get("memory_percent", 0) > self.thresholds["memory_usage"]:
                alerts.append({
                    "type": "high_memory",
                    "level": "warning",
                    "message": f"内存使用率过高: {sys_stats['memory_percent']}%",
                    "value": sys_stats["memory_percent"],
                    "threshold": self.thresholds["memory_usage"]
                })
        
        return alerts
    
    def print_status(self, health: Dict[str, Any], stats: Dict[str, Any], 
                    alerts: List[Dict[str, Any]], endpoints: Dict[str, bool]):
        """打印状态信息"""
        print("\n" + "="*80)
        print(f"性能监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 健康状态
        status_emoji = {
            "healthy": "🟢",
            "degraded": "🟡", 
            "unhealthy": "🔴",
            "unreachable": "⚫"
        }
        
        status = health.get("status", "unknown")
        print(f"应用状态: {status_emoji.get(status, '❓')} {status.upper()}")
        
        if "uptime_seconds" in health:
            uptime_hours = health["uptime_seconds"] / 3600
            print(f"运行时间: {uptime_hours:.1f} 小时")
        
        # 端点可用性
        print(f"\n端点可用性:")
        for endpoint, available in endpoints.items():
            emoji = "✅" if available else "❌"
            print(f"  {emoji} {endpoint}")
        
        # 请求统计
        if "request_stats" in stats:
            req_stats = stats["request_stats"]
            print(f"\n请求统计 (最近1小时):")
            print(f"  总请求数: {req_stats.get('total_requests', 0)}")
            print(f"  错误率: {req_stats.get('error_rate', 0):.2f}%")
            print(f"  平均响应时间: {req_stats.get('avg_response_time', 0):.3f}s")
            print(f"  请求频率: {req_stats.get('requests_per_minute', 0):.1f}/分钟")
            
            # 热门端点
            if req_stats.get('top_endpoints'):
                print(f"  热门端点:")
                for endpoint, count in req_stats['top_endpoints']:
                    print(f"    {endpoint}: {count} 次")
        
        # 系统资源
        if "system_stats" in stats:
            sys_stats = stats["system_stats"]
            print(f"\n系统资源:")
            print(f"  CPU使用率: {sys_stats.get('cpu_percent', 0):.1f}%")
            print(f"  内存使用率: {sys_stats.get('memory_percent', 0):.1f}%")
            print(f"  内存使用量: {sys_stats.get('memory_used_mb', 0):.1f} MB")
            print(f"  磁盘使用率: {sys_stats.get('disk_usage_percent', 0):.1f}%")
        
        # 告警信息
        if alerts:
            print(f"\n⚠️  活跃告警 ({len(alerts)}):")
            for alert in alerts:
                level_emoji = {"info": "ℹ️", "warning": "⚠️", "critical": "🚨"}
                emoji = level_emoji.get(alert.get("level", "info"), "❓")
                print(f"  {emoji} {alert.get('message', 'Unknown alert')}")
        else:
            print(f"\n✅ 无活跃告警")
        
        print("="*80)
    
    def save_report(self, health: Dict[str, Any], stats: Dict[str, Any], 
                   alerts: List[Dict[str, Any]]):
        """保存监控报告到文件"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "health": health,
            "stats": stats,
            "alerts": alerts
        }
        
        filename = f"monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"监控报告已保存到: {filename}")
        except Exception as e:
            print(f"保存报告失败: {e}")
    
    def run_once(self):
        """运行一次监控检查"""
        print(f"正在检查应用状态: {self.base_url}")
        
        # 获取各种监控数据
        health = self.check_health()
        stats = self.get_stats()
        endpoint_status = self.check_endpoint_availability()
        
        # 分析性能数据
        performance_alerts = self.analyze_performance(stats)
        
        # 获取应用内部告警
        app_alerts = self.get_alerts()
        all_alerts = performance_alerts + app_alerts.get("alerts", [])
        
        # 打印状态
        self.print_status(health, stats, all_alerts, endpoint_status)
        
        return health, stats, all_alerts
    
    def run_continuous(self):
        """持续监控模式"""
        print(f"开始持续监控 - 间隔: {self.interval}秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            while self.running:
                health, stats, alerts = self.run_once()
                
                # 检查是否需要保存报告
                if alerts or health.get("status") != "healthy":
                    self.save_report(health, stats, alerts)
                
                if self.running:
                    time.sleep(self.interval)
                    
        except KeyboardInterrupt:
            print("\n监控已停止")
            self.running = False
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止监控...")
        self.running = False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="性能监控工具")
    parser.add_argument("--url", default="http://localhost:8506", help="应用URL")
    parser.add_argument("--interval", type=int, default=30, help="监控间隔（秒）")
    parser.add_argument("--once", action="store_true", help="只运行一次检查")
    
    args = parser.parse_args()
    
    monitor = PerformanceMonitor(args.url, args.interval)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, monitor.signal_handler)
    signal.signal(signal.SIGTERM, monitor.signal_handler)
    
    if args.once:
        monitor.run_once()
    else:
        monitor.run_continuous()

if __name__ == "__main__":
    main()