"""
夸克网盘文件信息获取模块
"""

import requests
import json
import time
import logging
import urllib.parse
from datetime import datetime
from typing import Tuple, Optional, List
import urllib3

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)

# 夸克网盘请求头
quark_headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'no-cache',
    'dnt': '1',
    'origin': 'https://pan.quark.cn',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://pan.quark.cn/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
}

# 创建全局会话池以提高性能
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 创建会话池
session_pool = requests.Session()

# 配置连接池和重试策略
retry_strategy = Retry(
    total=3,
    backoff_factor=1,
    status_forcelist=[429, 500, 502, 503, 504],
)

adapter = HTTPAdapter(
    pool_connections=10,
    pool_maxsize=20,
    max_retries=retry_strategy
)

session_pool.mount("http://", adapter)
session_pool.mount("https://", adapter)

def get_quark_folder_info(link: str, max_retries: int = 3, retry_delay: int = 2) -> Tuple[Optional[str], Optional[str], Optional[int], Optional[int], Optional[int], Optional[List]]:
    """
    获取夸克分享链接的文件夹信息 - 优化版本
    
    Args:
        link: 夸克分享链接
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）
        
    Returns:
        Tuple[Optional[str], Optional[str], Optional[int], Optional[int], Optional[int], Optional[List]]: 
        - (文件夹名称, 更新时间, 文件总数量, 视频文件数量, 文件夹数量, 文件列表)
        - 如果获取失败，返回 (None, None, None, None, None, None)
    """
    try:
        for attempt in range(max_retries):
            try:
                # 提取短链接和pwd_id
                short_url = link.split('#')[0]
                pwd_id = short_url.split('/s/')[1]
                
                params = {
                    'pr': 'ucpro',
                    'fr': 'pc',
                    'uc_param_str': '',
                    '__dt': '1003',
                    '__t': str(int(time.time() * 1000)),
                }
                
                json_data = {
                    'pwd_id': pwd_id,
                    'passcode': '',
                }

                # 获取token
                token_url = 'https://drive-h.quark.cn/1/clouddrive/share/sharepage/token'
                response = session_pool.post(
                    token_url,
                    params=params,
                    headers=quark_headers,
                    json=json_data,
                    timeout=10,
                    verify=False
                )
                
                response_data = response.json()
                if response.status_code == 404 or response_data.get("status") == 404:
                    return None, None, None, None, None, None
                
                if "data" not in response_data or "stoken" not in response_data.get("data", {}):
                    return None, None, None, None, None, None
                
                stoken = response_data["data"]["stoken"]
                encoded_stoken = urllib.parse.quote(stoken, safe='')
                
                # 获取详情
                detail_url = f'https://drive-h.quark.cn/1/clouddrive/share/sharepage/detail?pr=ucpro&fr=pc&uc_param_str=&pwd_id={pwd_id}&stoken={encoded_stoken}&pdir_fid=0&force=0&_page=1&_size=200&_fetch_banner=1&_fetch_share=1&_fetch_total=1&_sort=file_type:asc,updated_at:desc&__dt=633&__t={str(int(time.time() * 1000))}'
                
                response = session_pool.get(
                    url=detail_url, 
                    headers=quark_headers,
                    timeout=10,
                    verify=False
                )
                data = response.json()
                
                if "data" not in data or "list" not in data["data"] or not data["data"]["list"]:
                    return None, None, None, None, None, None
                
                # 获取文件夹信息
                folder_name = data["data"]["list"][0]["file_name"]
                folder_update_time = datetime.fromtimestamp(data["data"]["list"][0]["last_update_at"]/1000).strftime('%Y-%m-%d %H:%M:%S')
                file_count = data["data"]["list"][0]["include_items"]
                
                # 获取文件列表
                file_list = data["data"]["list"]
                
                # 如果只有一个文件夹，获取该文件夹内的文件列表
                if len(file_list) == 1 and file_list[0]["dir"]:
                    folder_fid = file_list[0]["fid"]
                    folder_detail_url = f'https://drive-h.quark.cn/1/clouddrive/share/sharepage/detail?pr=ucpro&fr=pc&uc_param_str=&pwd_id={pwd_id}&stoken={encoded_stoken}&pdir_fid={folder_fid}&force=0&_page=1&_size=200&_fetch_banner=1&_fetch_share=1&_fetch_total=1&_sort=file_type:asc,updated_at:desc&__dt=633&__t={str(int(time.time() * 1000))}'
                    
                    response = session_pool.get(
                        url=folder_detail_url, 
                        headers=quark_headers,
                        timeout=10,
                        verify=False
                    )
                    folder_data = response.json()
                    
                    if "data" in folder_data and "list" in folder_data["data"]:
                        file_list = folder_data["data"]["list"]
                
                # 统计视频文件和文件夹数量
                video_count = 0
                folder_count = 0

                def is_video_file(item):
                    """判断是否为视频文件"""
                    if item.get("dir", False):
                        return False

                    format_type = item.get("format_type", "")
                    if format_type.startswith("video/"):
                        return True

                    obj_category = item.get("obj_category", "")
                    if obj_category == "video":
                        return True

                    file_name = item.get("file_name", "")
                    if "." in file_name:
                        file_ext = file_name.split(".")[-1].lower()
                        video_extensions = [
                            'mp4', 'mkv', 'avi', 'mov', 'wmv', 'flv', 'm4v',
                            'webm', 'ts', 'm2ts', 'rmvb', 'rm', '3gp', 'asf',
                            'mpg', 'mpeg', 'vob', 'f4v', 'swf'
                        ]
                        if file_ext in video_extensions:
                            return True

                    return False

                for item in file_list:
                    if item.get("dir", False):
                        folder_count += 1
                    elif is_video_file(item):
                        video_count += 1
                
                return folder_name, folder_update_time, file_count, video_count, folder_count, file_list
                
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"获取分享信息失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(retry_delay)
                    continue
                raise
                
    except Exception as e:
        logger.error(f"获取分享信息失败: {e}")
        return None, None, None, None, None, None