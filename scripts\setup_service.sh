#!/bin/bash

# 系统服务配置脚本
# 配置systemd服务和supervisor

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
APP_NAME="movie-search"
APP_USER="movieapp"
APP_DIR="/opt/movie-search"
SERVICE_NAME="movie-search"

# 创建systemd服务文件
create_systemd_service() {
    log_info "创建systemd服务文件..."
    
    sudo tee "/etc/systemd/system/${SERVICE_NAME}.service" > /dev/null <<EOF
[Unit]
Description=Movie Search Application
After=network.target
Wants=network.target

[Service]
Type=exec
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
Environment=PATH=$APP_DIR/venv/bin
EnvironmentFile=$APP_DIR/.env
ExecStart=$APP_DIR/venv/bin/python -m uvicorn app.main:app --host \${HOST} --port \${PORT} --workers \${WORKERS}
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=5
StartLimitInterval=60s
StartLimitBurst=3

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR/logs $APP_DIR/backups
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "systemd服务文件创建完成"
}

# 创建supervisor配置文件（备选方案）
create_supervisor_config() {
    log_info "创建supervisor配置文件..."
    
    sudo tee "/etc/supervisor/conf.d/${SERVICE_NAME}.conf" > /dev/null <<EOF
[program:$SERVICE_NAME]
command=$APP_DIR/venv/bin/python -m uvicorn app.main:app --host 127.0.0.1 --port 8506 --workers 2
directory=$APP_DIR
user=$APP_USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$APP_DIR/logs/supervisor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="$APP_DIR/venv/bin"
EOF
    
    log_success "supervisor配置文件创建完成"
}

# 创建启动脚本
create_start_script() {
    log_info "创建启动脚本..."
    
    sudo tee "$APP_DIR/start.sh" > /dev/null <<'EOF'
#!/bin/bash

# 移动端电影搜索应用启动脚本

APP_DIR="/opt/movie-search"
SERVICE_NAME="movie-search"

# 加载环境变量
if [ -f "$APP_DIR/.env" ]; then
    export $(cat "$APP_DIR/.env" | grep -v '^#' | xargs)
fi

# 检查虚拟环境
if [ ! -d "$APP_DIR/venv" ]; then
    echo "错误: 虚拟环境不存在"
    exit 1
fi

# 检查应用文件
if [ ! -f "$APP_DIR/app/main.py" ]; then
    echo "错误: 应用文件不存在"
    exit 1
fi

# 创建日志目录
mkdir -p "$APP_DIR/logs"

# 启动应用
cd "$APP_DIR"
exec "$APP_DIR/venv/bin/python" -m uvicorn app.main:app \
    --host "${HOST:-127.0.0.1}" \
    --port "${PORT:-8506}" \
    --workers "${WORKERS:-2}" \
    --log-level "${LOG_LEVEL:-info}" \
    --access-log \
    --no-use-colors
EOF
    
    sudo chmod +x "$APP_DIR/start.sh"
    sudo chown "$APP_USER:$APP_USER" "$APP_DIR/start.sh"
    
    log_success "启动脚本创建完成"
}

# 创建停止脚本
create_stop_script() {
    log_info "创建停止脚本..."
    
    sudo tee "$APP_DIR/stop.sh" > /dev/null <<EOF
#!/bin/bash

# 移动端电影搜索应用停止脚本

SERVICE_NAME="movie-search"

# 停止systemd服务
if systemctl is-active --quiet "\$SERVICE_NAME"; then
    echo "停止systemd服务..."
    sudo systemctl stop "\$SERVICE_NAME"
fi

# 停止supervisor进程
if command -v supervisorctl &> /dev/null; then
    if supervisorctl status "\$SERVICE_NAME" &> /dev/null; then
        echo "停止supervisor进程..."
        sudo supervisorctl stop "\$SERVICE_NAME"
    fi
fi

# 强制杀死进程
PIDS=\$(pgrep -f "uvicorn app.main:app")
if [ -n "\$PIDS" ]; then
    echo "强制停止应用进程..."
    sudo kill -TERM \$PIDS
    sleep 5
    
    # 如果还在运行，强制杀死
    PIDS=\$(pgrep -f "uvicorn app.main:app")
    if [ -n "\$PIDS" ]; then
        sudo kill -KILL \$PIDS
    fi
fi

echo "应用已停止"
EOF
    
    sudo chmod +x "$APP_DIR/stop.sh"
    sudo chown "$APP_USER:$APP_USER" "$APP_DIR/stop.sh"
    
    log_success "停止脚本创建完成"
}

# 创建重启脚本
create_restart_script() {
    log_info "创建重启脚本..."
    
    sudo tee "$APP_DIR/restart.sh" > /dev/null <<EOF
#!/bin/bash

# 移动端电影搜索应用重启脚本

APP_DIR="/opt/movie-search"

echo "重启应用..."
"\$APP_DIR/stop.sh"
sleep 2
"\$APP_DIR/start.sh"
EOF
    
    sudo chmod +x "$APP_DIR/restart.sh"
    sudo chown "$APP_USER:$APP_USER" "$APP_DIR/restart.sh"
    
    log_success "重启脚本创建完成"
}

# 配置日志轮转
setup_log_rotation() {
    log_info "配置日志轮转..."
    
    sudo tee "/etc/logrotate.d/$SERVICE_NAME" > /dev/null <<EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        systemctl reload $SERVICE_NAME 2>/dev/null || true
    endscript
}
EOF
    
    log_success "日志轮转配置完成"
}

# 启用和启动服务
enable_and_start_service() {
    log_info "启用和启动服务..."
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable "$SERVICE_NAME"
    
    # 启动服务
    sudo systemctl start "$SERVICE_NAME"
    
    # 检查服务状态
    sleep 3
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
        sudo systemctl status "$SERVICE_NAME" --no-pager -l
    else
        log_error "服务启动失败"
        sudo systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始配置系统服务..."
    
    create_systemd_service
    create_supervisor_config
    create_start_script
    create_stop_script
    create_restart_script
    setup_log_rotation
    enable_and_start_service
    
    log_success "系统服务配置完成！"
    log_info "服务管理命令："
    log_info "  启动: sudo systemctl start $SERVICE_NAME"
    log_info "  停止: sudo systemctl stop $SERVICE_NAME"
    log_info "  重启: sudo systemctl restart $SERVICE_NAME"
    log_info "  状态: sudo systemctl status $SERVICE_NAME"
    log_info "  日志: sudo journalctl -u $SERVICE_NAME -f"
}

# 错误处理
trap 'log_error "服务配置过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
