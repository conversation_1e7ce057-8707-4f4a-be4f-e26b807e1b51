#!/usr/bin/env python3
"""
测试搜索API功能
"""

import requests
import json
import time

def test_search_api():
    """测试搜索API"""
    
    base_url = "http://localhost:8506"
    
    print("🧪 测试搜索API功能")
    print("=" * 50)
    
    # 测试电影名称
    test_movies = [
        "利剑·玫瑰",
        "哈利·波特",
        "复仇者联盟",
        "蜘蛛侠：英雄归来",
        "变形金刚",
        "阿凡达：水之道"
    ]
    
    # 首先获取会话令牌
    try:
        print("📝 获取会话令牌...")
        session_response = requests.post(f"{base_url}/api/session")
        if session_response.status_code == 200:
            session_data = session_response.json()
            session_token = session_data.get('session_token')
            print(f"✅ 会话令牌获取成功: {session_token[:20]}...")
        else:
            print(f"❌ 会话令牌获取失败: {session_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 会话令牌获取异常: {e}")
        return False
    
    # 测试搜索功能
    headers = {
        'Content-Type': 'application/json',
        'X-Session-Token': session_token
    }
    
    success_count = 0
    total_count = len(test_movies)
    
    for movie_name in test_movies:
        try:
            print(f"\n🔍 搜索: {movie_name}")
            
            # 发送搜索请求
            search_data = {"keyword": movie_name}
            response = requests.post(
                f"{base_url}/api/search",
                headers=headers,
                json=search_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results_count = len(result.get('data', []))
                    print(f"✅ 搜索成功: 找到 {results_count} 个结果")
                    success_count += 1
                else:
                    print(f"⚠️ 搜索无结果: {result.get('message', '未知错误')}")
                    success_count += 1  # 无结果也算成功（验证通过了）
            elif response.status_code == 400:
                print(f"❌ 搜索失败 (400): {response.text}")
            elif response.status_code == 429:
                print(f"⚠️ 频率限制: {response.text}")
                time.sleep(2)  # 等待2秒后继续
            else:
                print(f"❌ 搜索失败 ({response.status_code}): {response.text}")
                
        except Exception as e:
            print(f"❌ 搜索异常: {e}")
        
        # 避免频率限制
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_count} 成功")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有搜索测试通过！")
        return True
    else:
        print("⚠️ 部分搜索测试失败")
        return False

def test_health_check():
    """测试健康检查"""
    
    print("\n🏥 测试健康检查")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8506/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 健康检查通过")
            print(f"   状态: {health_data.get('status')}")
            print(f"   服务: {health_data.get('service')}")
            print(f"   运行时间: {health_data.get('uptime', 0):.2f}秒")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 API功能测试")
    print("=" * 60)
    
    # 测试健康检查
    health_ok = test_health_check()
    
    if health_ok:
        # 测试搜索功能
        search_ok = test_search_api()
        
        print("\n" + "=" * 60)
        if search_ok:
            print("🎉 所有API测试通过！")
            print("✅ 搜索验证问题已修复")
        else:
            print("❌ API测试失败")
    else:
        print("❌ 应用未运行或健康检查失败")
        print("请先启动应用: python start.py")
