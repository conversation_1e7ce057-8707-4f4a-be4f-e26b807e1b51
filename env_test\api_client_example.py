#!/usr/bin/env python3
"""
API客户端使用示例
演示如何正确调用加密的API
"""

import requests
import json
import time
import hmac
import hashlib

class MovieSearchAPIClient:
    """电影搜索API客户端"""
    
    def __init__(self, base_url, api_key, api_secret):
        self.base_url = base_url
        self.api_key = api_key
        self.api_secret = api_secret
        self.session = requests.Session()
        
        # 设置正常的用户代理
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def _generate_signature(self, method, path, timestamp, body=""):
        """生成请求签名"""
        sign_string = f"{method}\n{path}\n{timestamp}\n{body}"
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def _make_authenticated_request(self, method, path, data=None):
        """发送认证请求"""
        timestamp = str(int(time.time()))
        body = json.dumps(data) if data else ""
        
        # 生成签名
        signature = self._generate_signature(method, path, timestamp, body)
        
        # 设置请求头
        headers = {
            "X-API-Key": self.api_key,
            "X-Timestamp": timestamp,
            "X-Signature": signature,
            "Content-Type": "application/json"
        }
        
        # 发送请求
        url = f"{self.base_url}{path}"
        try:
            if method == "GET":
                response = self.session.get(url, headers=headers, timeout=30)
            else:
                response = self.session.post(url, headers=headers, data=body, timeout=30)
            
            return response
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
    
    def search_movies(self, keyword):
        """搜索电影"""
        print(f"\n🔍 搜索电影: {keyword}")
        
        response = self._make_authenticated_request("POST", "/api/search", {
            "keyword": keyword
        })
        
        if response and response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ 搜索成功，找到 {data.get('total', 0)} 个结果")
                print(f"⏱️ 搜索耗时: {data.get('search_time', 0)} 秒")
                print(f"🔐 链接加密状态: {data.get('security', {}).get('links_encrypted', False)}")
                print(f"👤 认证用户: {data.get('security', {}).get('authenticated_user', 'Unknown')}")
                
                return data.get("data", [])
            else:
                print(f"❌ 搜索失败: {data.get('message', '未知错误')}")
        elif response:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {error_data.get('detail', '未知错误')}")
            except:
                print(f"错误详情: {response.text}")
        else:
            print("❌ 请求失败: 无响应")
        
        return []

    def auth_search_movies(self, keyword):
        """认证搜索电影（返回加密链接）"""
        print(f"\n🔐 认证搜索电影: {keyword}")

        response = self._make_authenticated_request("POST", "/api/auth/search", {
            "keyword": keyword
        })

        if response and response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ 认证搜索成功，找到 {data.get('total', 0)} 个结果")
                print(f"⏱️ 搜索耗时: {data.get('search_time', 0)} 秒")
                print(f"🔐 链接加密状态: {data.get('security', {}).get('links_encrypted', False)}")
                print(f"👤 认证用户: {data.get('security', {}).get('authenticated_user', 'Unknown')}")

                return data.get("data", [])
            else:
                print(f"❌ 认证搜索失败: {data.get('message', '未知错误')}")
        elif response:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {error_data.get('detail', '未知错误')}")
            except:
                print(f"错误详情: {response.text}")
        else:
            print("❌ 请求失败: 无响应")

        return []

    def search_cloudsave(self, keyword):
        """CloudSave搜索"""
        print(f"\n☁️ CloudSave搜索: {keyword}")
        
        response = self._make_authenticated_request("POST", "/api/search-cloudsave", {
            "keyword": keyword
        })
        
        if response and response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ CloudSave搜索成功")
                print(f"📁 云盘类型数: {data.get('total_cloud_types', 0)}")
                print(f"📄 总资源数: {data.get('total_results', 0)}")
                print(f"⏱️ 搜索耗时: {data.get('search_time', 0)} 秒")
                
                return data.get("data", {})
            else:
                print(f"❌ CloudSave搜索失败: {data.get('message', '未知错误')}")
        elif response:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {error_data.get('detail', '未知错误')}")
            except:
                print(f"错误详情: {response.text}")
        else:
            print("❌ 请求失败: 无响应")
        
        return {}
    
    def decrypt_link(self, encrypted_link):
        """解密链接"""
        print(f"\n🔓 解密链接: {encrypted_link[:50]}...")
        
        response = self._make_authenticated_request("POST", "/api/decrypt-link", {
            "encrypted_link": encrypted_link
        })
        
        if response and response.status_code == 200:
            data = response.json()
            if data.get("success"):
                decrypted_link = data.get("decrypted_link")
                print(f"✅ 链接解密成功")
                print(f"🔗 原始链接: {decrypted_link}")
                return decrypted_link
            else:
                print(f"❌ 链接解密失败: {data.get('message', '未知错误')}")
        elif response:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误详情: {error_data.get('detail', '未知错误')}")
            except:
                print(f"错误详情: {response.text}")
        else:
            print("❌ 请求失败: 无响应")
        
        return None
    
    def get_hot_movies(self):
        """获取热门电影"""
        print(f"\n🔥 获取热门电影")
        
        response = self._make_authenticated_request("GET", "/api/hot-movies")
        
        if response and response.status_code == 200:
            data = response.json()
            if data.get("success"):
                movies = data.get("data", [])
                print(f"✅ 获取热门电影成功，共 {len(movies)} 部")
                
                for i, movie in enumerate(movies[:5], 1):  # 只显示前5部
                    if isinstance(movie, dict):
                        print(f"  {i}. {movie.get('title', '未知标题')}")
                    else:
                        print(f"  {i}. {movie}")
                
                return movies
            else:
                print(f"❌ 获取热门电影失败: {data.get('message', '未知错误')}")
        elif response:
            print(f"❌ 请求失败: HTTP {response.status_code}")
        else:
            print("❌ 请求失败: 无响应")
        
        return []

def main():
    """主函数"""
    print("🎬 电影搜索API客户端示例")
    print("=" * 50)
    
    # 配置
    API_BASE_URL = "http://localhost:8080"
    API_KEY = "demo_key_123456"
    API_SECRET = "your-secret-key-change-in-production"
    
    # 创建客户端
    client = MovieSearchAPIClient(API_BASE_URL, API_KEY, API_SECRET)
    
    try:
        # 1. 获取热门电影
        hot_movies = client.get_hot_movies()
        
        # 2. 搜索电影（认证版本）
        search_results = client.auth_search_movies("复仇者联盟")
        
        # 3. 如果有搜索结果，尝试解密第一个链接
        if search_results and search_results[0].get("links"):
            first_link = search_results[0]["links"][0]
            encrypted_url = first_link.get("url")
            
            if encrypted_url and encrypted_url.startswith("encrypted:"):
                decrypted_url = client.decrypt_link(encrypted_url)
                if decrypted_url:
                    print(f"🎯 链接类型: {first_link.get('type', '未知')}")
                    print(f"📁 文件夹名: {first_link.get('folder_name', '未知')}")
                    if first_link.get('video_count'):
                        print(f"🎥 视频文件数: {first_link.get('video_count')}")
        
        # 4. CloudSave搜索
        cloudsave_results = client.search_cloudsave("复仇者联盟")
        
        # 5. 演示频率限制（立即再次搜索）
        print(f"\n⚠️ 演示频率限制 - 立即再次搜索...")
        time.sleep(1)  # 稍微等待一下
        client.search_movies("钢铁侠")
        
        print(f"\n✅ API客户端示例演示完成！")
        print(f"📝 请查看应用日志以确认安全功能正常工作")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
