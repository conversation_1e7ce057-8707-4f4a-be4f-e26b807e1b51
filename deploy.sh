#!/bin/bash

# 移动端电影搜索应用 - 生产环境部署脚本
# 使用方法: ./deploy.sh [production|staging]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    log_error "环境参数错误。使用方法: ./deploy.sh [production|staging]"
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境..."

# 配置变量
APP_NAME="movie-search-app"
IMAGE_NAME="$APP_NAME:latest"
CONTAINER_NAME="$APP_NAME-$ENVIRONMENT"
NETWORK_NAME="$APP_NAME-network"

# 生产环境配置
if [[ "$ENVIRONMENT" == "production" ]]; then
    PORT=8506
    DOMAIN="yourdomain.com"
    ALLOWED_ORIGINS="https://$DOMAIN,https://www.$DOMAIN"
    LOG_LEVEL="WARNING"
    WORKERS=2
else
    PORT=8507
    DOMAIN="staging.yourdomain.com"
    ALLOWED_ORIGINS="https://$DOMAIN"
    LOG_LEVEL="INFO"
    WORKERS=1
fi

# 检查必要工具
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    if ! command -v nginx &> /dev/null; then
        log_warning "Nginx 未安装，建议安装用于反向代理"
    fi
    
    log_success "系统要求检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p backups
    mkdir -p ssl
    
    # 设置权限
    chmod 755 logs
    chmod 700 ssl
    
    log_success "目录创建完成"
}

# 备份现有部署
backup_current_deployment() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "备份当前部署..."
        
        BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # 备份日志
        if [[ -d "logs" ]]; then
            cp -r logs "$BACKUP_DIR/"
        fi
        
        # 备份配置
        docker inspect "$CONTAINER_NAME" > "$BACKUP_DIR/container_config.json" 2>/dev/null || true
        
        log_success "备份完成: $BACKUP_DIR"
    fi
}

# 停止现有容器
stop_existing_container() {
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_info "停止现有容器..."
        docker stop "$CONTAINER_NAME"
        log_success "容器已停止"
    fi
    
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "删除现有容器..."
        docker rm "$CONTAINER_NAME"
        log_success "容器已删除"
    fi
}

# 构建镜像
build_image() {
    log_info "构建Docker镜像..."
    
    # 检查Dockerfile是否存在
    if [[ ! -f "Dockerfile" ]]; then
        log_error "Dockerfile 不存在"
        exit 1
    fi
    
    # 构建镜像
    docker build -t "$IMAGE_NAME" . --no-cache
    
    log_success "镜像构建完成"
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        log_info "创建Docker网络..."
        docker network create "$NETWORK_NAME"
        log_success "网络创建完成"
    fi
}

# 启动容器
start_container() {
    log_info "启动应用容器..."
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        --network "$NETWORK_NAME" \
        -p "$PORT:8506" \
        -v "$(pwd)/logs:/app/logs:rw" \
        -v "/etc/localtime:/etc/localtime:ro" \
        -e ENVIRONMENT="$ENVIRONMENT" \
        -e PYTHONPATH=/app \
        -e PYTHONUNBUFFERED=1 \
        -e PYTHONDONTWRITEBYTECODE=1 \
        -e TZ=Asia/Shanghai \
        -e ALLOWED_ORIGINS="$ALLOWED_ORIGINS" \
        -e LOG_LEVEL="$LOG_LEVEL" \
        -e SEARCH_RATE_LIMIT=3 \
        -e HOT_MOVIES_RATE_LIMIT=10 \
        -e MAX_VIOLATIONS=5 \
        -e BLOCK_DURATION=120 \
        --restart unless-stopped \
        --memory=512m \
        --cpus=0.5 \
        "$IMAGE_NAME"
    
    log_success "容器启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待容器启动
    sleep 10
    
    # 检查容器状态
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "容器未正常运行"
        docker logs "$CONTAINER_NAME" --tail 20
        exit 1
    fi
    
    # 检查应用健康状态
    for i in {1..30}; do
        if curl -f "http://localhost:$PORT/health/live" &>/dev/null; then
            log_success "应用健康检查通过"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            log_error "应用健康检查失败"
            docker logs "$CONTAINER_NAME" --tail 20
            exit 1
        fi
        
        log_info "等待应用启动... ($i/30)"
        sleep 2
    done
}

# 生成Nginx配置
generate_nginx_config() {
    log_info "生成Nginx配置..."
    
    cat > "/tmp/nginx_$ENVIRONMENT.conf" << EOF
server {
    listen 80;
    server_name $DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/$DOMAIN.crt;
    ssl_certificate_key /etc/ssl/private/$DOMAIN.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # 限制请求大小
    client_max_body_size 1M;
    
    # 频率限制
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/m;
    limit_req_zone \$binary_remote_addr zone=search:10m rate=5r/m;
    
    # 主要位置
    location / {
        proxy_pass http://127.0.0.1:$PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # API搜索接口特殊限制
    location /api/search {
        limit_req zone=search burst=3 nodelay;
        proxy_pass http://127.0.0.1:$PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 静态文件缓存
    location /static/ {
        proxy_pass http://127.0.0.1:$PORT;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:$PORT;
        access_log off;
    }
}
EOF
    
    log_success "Nginx配置已生成: /tmp/nginx_$ENVIRONMENT.conf"
    log_warning "请手动将配置复制到Nginx配置目录并重载Nginx"
}

# 设置日志轮转
setup_log_rotation() {
    log_info "设置日志轮转..."
    
    cat > "/tmp/logrotate_$APP_NAME" << EOF
$(pwd)/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker kill -s USR1 $CONTAINER_NAME 2>/dev/null || true
    endscript
}
EOF
    
    log_success "日志轮转配置已生成: /tmp/logrotate_$APP_NAME"
    log_warning "请手动将配置复制到 /etc/logrotate.d/"
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "环境: $ENVIRONMENT"
    echo "容器名称: $CONTAINER_NAME"
    echo "端口: $PORT"
    echo "域名: $DOMAIN"
    echo "日志目录: $(pwd)/logs"
    echo
    echo "=== 有用的命令 ==="
    echo "查看容器状态: docker ps | grep $CONTAINER_NAME"
    echo "查看容器日志: docker logs -f $CONTAINER_NAME"
    echo "进入容器: docker exec -it $CONTAINER_NAME /bin/bash"
    echo "重启容器: docker restart $CONTAINER_NAME"
    echo "停止容器: docker stop $CONTAINER_NAME"
    echo
    echo "=== 访问地址 ==="
    echo "应用首页: http://localhost:$PORT"
    echo "健康检查: http://localhost:$PORT/health"
    echo "API文档: http://localhost:$PORT/docs"
    echo "监控面板: http://localhost:$PORT/monitoring"
    echo
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "=== 生产环境提醒 ==="
        echo "1. 请配置SSL证书"
        echo "2. 请设置Nginx反向代理"
        echo "3. 请配置防火墙规则"
        echo "4. 请设置监控告警"
        echo "5. 请定期备份数据"
    fi
}

# 主函数
main() {
    log_info "开始部署 $APP_NAME 到 $ENVIRONMENT 环境"
    
    check_requirements
    create_directories
    backup_current_deployment
    stop_existing_container
    build_image
    create_network
    start_container
    health_check
    generate_nginx_config
    setup_log_rotation
    show_deployment_info
    
    log_success "部署流程完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
