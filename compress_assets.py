#!/usr/bin/env python3
"""
资源压缩脚本 - 压缩 CSS 和 JavaScript 文件以提高性能
"""

import os
import re
import gzip
import shutil
from pathlib import Path

def minify_css(css_content):
    """压缩 CSS 内容"""
    # 移除注释
    css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
    
    # 移除多余的空白字符
    css_content = re.sub(r'\s+', ' ', css_content)
    
    # 移除不必要的空格
    css_content = re.sub(r';\s*}', '}', css_content)
    css_content = re.sub(r'{\s*', '{', css_content)
    css_content = re.sub(r'}\s*', '}', css_content)
    css_content = re.sub(r':\s*', ':', css_content)
    css_content = re.sub(r';\s*', ';', css_content)
    css_content = re.sub(r',\s*', ',', css_content)
    
    # 移除最后的分号
    css_content = re.sub(r';}', '}', css_content)
    
    return css_content.strip()

def minify_js(js_content):
    """简单的 JavaScript 压缩"""
    # 移除单行注释
    js_content = re.sub(r'//.*$', '', js_content, flags=re.MULTILINE)
    
    # 移除多行注释
    js_content = re.sub(r'/\*.*?\*/', '', js_content, flags=re.DOTALL)
    
    # 移除多余的空白字符，但保留字符串内的空格
    lines = js_content.split('\n')
    minified_lines = []
    
    for line in lines:
        line = line.strip()
        if line:
            minified_lines.append(line)
    
    # 简单的空格压缩
    js_content = ' '.join(minified_lines)
    js_content = re.sub(r'\s*{\s*', '{', js_content)
    js_content = re.sub(r'\s*}\s*', '}', js_content)
    js_content = re.sub(r'\s*;\s*', ';', js_content)
    js_content = re.sub(r'\s*,\s*', ',', js_content)
    js_content = re.sub(r'\s*\(\s*', '(', js_content)
    js_content = re.sub(r'\s*\)\s*', ')', js_content)
    
    return js_content

def create_gzip_version(file_path):
    """创建文件的 gzip 版本"""
    with open(file_path, 'rb') as f_in:
        with gzip.open(f"{file_path}.gz", 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
    
    print(f"创建 gzip 版本: {file_path}.gz")

def compress_assets():
    """压缩静态资源"""
    static_dir = Path('static')
    
    if not static_dir.exists():
        print("static 目录不存在")
        return
    
    # 压缩 CSS 文件
    css_file = static_dir / 'style.css'
    if css_file.exists():
        print("压缩 CSS 文件...")
        with open(css_file, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        minified_css = minify_css(css_content)
        
        # 保存压缩版本
        min_css_file = static_dir / 'style.min.css'
        with open(min_css_file, 'w', encoding='utf-8') as f:
            f.write(minified_css)
        
        print(f"CSS 压缩完成: {css_file} -> {min_css_file}")
        print(f"原始大小: {len(css_content)} 字节")
        print(f"压缩后大小: {len(minified_css)} 字节")
        print(f"压缩率: {((len(css_content) - len(minified_css)) / len(css_content) * 100):.1f}%")
        
        # 创建 gzip 版本
        create_gzip_version(min_css_file)
    
    # 压缩 JavaScript 文件
    js_file = static_dir / 'script.js'
    if js_file.exists():
        print("\n压缩 JavaScript 文件...")
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        minified_js = minify_js(js_content)
        
        # 保存压缩版本
        min_js_file = static_dir / 'script.min.js'
        with open(min_js_file, 'w', encoding='utf-8') as f:
            f.write(minified_js)
        
        print(f"JavaScript 压缩完成: {js_file} -> {min_js_file}")
        print(f"原始大小: {len(js_content)} 字节")
        print(f"压缩后大小: {len(minified_js)} 字节")
        print(f"压缩率: {((len(js_content) - len(minified_js)) / len(js_content) * 100):.1f}%")
        
        # 创建 gzip 版本
        create_gzip_version(min_js_file)
    
    print("\n资源压缩完成！")

if __name__ == '__main__':
    compress_assets()