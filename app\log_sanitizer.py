"""
日志脱敏模块
"""

import re
import logging
import hashlib
from typing import Any, Dict, List
from security_config import security_config

class LogSanitizer:
    """日志脱敏器"""
    
    def __init__(self):
        self.enable_sanitization = security_config.ENABLE_LOG_SANITIZATION
        self.sensitive_patterns = security_config.SENSITIVE_PATTERNS
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE) for pattern in self.sensitive_patterns
        ]
    
    def _hash_sensitive_data(self, data: str) -> str:
        """
        对敏感数据进行哈希处理
        
        Args:
            data: 敏感数据
            
        Returns:
            str: 哈希后的数据
        """
        # 使用SHA256哈希，只保留前8位用于调试
        hash_value = hashlib.sha256(data.encode('utf-8')).hexdigest()[:8]
        return f"***{hash_value}"
    
    def sanitize_text(self, text: str) -> str:
        """
        脱敏文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 脱敏后的文本
        """
        if not self.enable_sanitization or not text:
            return text
        
        sanitized_text = text
        
        # 应用所有脱敏规则
        for pattern in self.compiled_patterns:
            def replace_func(match):
                matched_text = match.group(0)
                # 对匹配的敏感信息进行哈希处理
                return self._hash_sensitive_data(matched_text)
            
            sanitized_text = pattern.sub(replace_func, sanitized_text)
        
        # 额外的IP地址脱敏（保留前两段）
        ip_pattern = re.compile(r'\b(\d{1,3}\.\d{1,3})\.\d{1,3}\.\d{1,3}\b')
        sanitized_text = ip_pattern.sub(r'\1.***.***', sanitized_text)
        
        return sanitized_text
    
    def sanitize_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        脱敏字典数据
        
        Args:
            data: 原始字典
            
        Returns:
            Dict[str, Any]: 脱敏后的字典
        """
        if not self.enable_sanitization:
            return data
        
        sanitized_data = {}
        
        for key, value in data.items():
            # 检查键名是否包含敏感信息
            if any(sensitive_key in key.lower() for sensitive_key in 
                   ['password', 'token', 'key', 'secret', 'api_key', 'signature']):
                sanitized_data[key] = self._hash_sensitive_data(str(value))
            elif isinstance(value, str):
                sanitized_data[key] = self.sanitize_text(value)
            elif isinstance(value, dict):
                sanitized_data[key] = self.sanitize_dict(value)
            elif isinstance(value, list):
                sanitized_data[key] = [
                    self.sanitize_text(item) if isinstance(item, str) 
                    else self.sanitize_dict(item) if isinstance(item, dict)
                    else item
                    for item in value
                ]
            else:
                sanitized_data[key] = value
        
        return sanitized_data
    
    def sanitize_url(self, url: str) -> str:
        """
        脱敏URL（特别是网盘链接）
        
        Args:
            url: 原始URL
            
        Returns:
            str: 脱敏后的URL
        """
        if not self.enable_sanitization or not url:
            return url
        
        # 检查是否是网盘链接
        if any(domain in url.lower() for domain in ['pan.baidu.com', 'drive.uc.cn', 'quark.cn']):
            # 提取域名和路径的第一部分
            import urllib.parse
            parsed = urllib.parse.urlparse(url)
            domain = parsed.netloc
            path_parts = parsed.path.split('/')
            
            if len(path_parts) > 2:
                # 保留域名和第一级路径，其余用哈希替代
                safe_path = f"/{path_parts[1]}/***{self._hash_sensitive_data(url)}"
                return f"{parsed.scheme}://{domain}{safe_path}"
        
        return self.sanitize_text(url)

class SanitizedLogger:
    """脱敏日志记录器"""
    
    def __init__(self, logger_name: str):
        self.logger = logging.getLogger(logger_name)
        self.sanitizer = LogSanitizer()
    
    def _sanitize_args(self, *args) -> tuple:
        """脱敏日志参数"""
        return tuple(
            self.sanitizer.sanitize_text(str(arg)) if isinstance(arg, str)
            else self.sanitizer.sanitize_dict(arg) if isinstance(arg, dict)
            else arg
            for arg in args
        )
    
    def info(self, msg: str, *args, **kwargs):
        """记录INFO级别日志"""
        sanitized_msg = self.sanitizer.sanitize_text(msg)
        sanitized_args = self._sanitize_args(*args)
        self.logger.info(sanitized_msg, *sanitized_args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """记录WARNING级别日志"""
        sanitized_msg = self.sanitizer.sanitize_text(msg)
        sanitized_args = self._sanitize_args(*args)
        self.logger.warning(sanitized_msg, *sanitized_args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """记录ERROR级别日志"""
        sanitized_msg = self.sanitizer.sanitize_text(msg)
        sanitized_args = self._sanitize_args(*args)
        self.logger.error(sanitized_msg, *sanitized_args, **kwargs)
    
    def debug(self, msg: str, *args, **kwargs):
        """记录DEBUG级别日志"""
        sanitized_msg = self.sanitizer.sanitize_text(msg)
        sanitized_args = self._sanitize_args(*args)
        self.logger.debug(sanitized_msg, *sanitized_args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """记录CRITICAL级别日志"""
        sanitized_msg = self.sanitizer.sanitize_text(msg)
        sanitized_args = self._sanitize_args(*args)
        self.logger.critical(sanitized_msg, *sanitized_args, **kwargs)

class AccessControlLogger:
    """访问控制日志记录器"""
    
    def __init__(self):
        self.sanitizer = LogSanitizer()
        self.access_logger = logging.getLogger("access_control")
        
        # 配置访问控制日志处理器
        if not self.access_logger.handlers:
            handler = logging.FileHandler("logs/access_control.log")
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.access_logger.addHandler(handler)
            self.access_logger.setLevel(logging.INFO)
    
    def log_api_access(self, client_ip: str, api_key: str, username: str, 
                      endpoint: str, success: bool, error_msg: str = ""):
        """
        记录API访问日志
        
        Args:
            client_ip: 客户端IP
            api_key: API密钥
            username: 用户名
            endpoint: 访问的端点
            success: 是否成功
            error_msg: 错误信息
        """
        # 脱敏处理
        sanitized_ip = self.sanitizer.sanitize_text(client_ip)
        sanitized_key = self.sanitizer._hash_sensitive_data(api_key) if api_key else "None"
        sanitized_endpoint = self.sanitizer.sanitize_text(endpoint)
        sanitized_error = self.sanitizer.sanitize_text(error_msg) if error_msg else ""
        
        status = "SUCCESS" if success else "FAILED"
        
        log_msg = (
            f"API_ACCESS - IP: {sanitized_ip} | "
            f"API_KEY: {sanitized_key} | "
            f"USER: {username} | "
            f"ENDPOINT: {sanitized_endpoint} | "
            f"STATUS: {status}"
        )
        
        if error_msg:
            log_msg += f" | ERROR: {sanitized_error}"
        
        if success:
            self.access_logger.info(log_msg)
        else:
            self.access_logger.warning(log_msg)
    
    def log_rate_limit_violation(self, client_ip: str, endpoint: str, 
                                violation_count: int, blocked: bool = False):
        """
        记录频率限制违规日志
        
        Args:
            client_ip: 客户端IP
            endpoint: 访问的端点
            violation_count: 违规次数
            blocked: 是否被阻止
        """
        sanitized_ip = self.sanitizer.sanitize_text(client_ip)
        sanitized_endpoint = self.sanitizer.sanitize_text(endpoint)
        
        status = "BLOCKED" if blocked else "WARNING"
        
        log_msg = (
            f"RATE_LIMIT_VIOLATION - IP: {sanitized_ip} | "
            f"ENDPOINT: {sanitized_endpoint} | "
            f"VIOLATIONS: {violation_count} | "
            f"STATUS: {status}"
        )
        
        if blocked:
            self.access_logger.error(log_msg)
        else:
            self.access_logger.warning(log_msg)

# 创建全局实例
log_sanitizer = LogSanitizer()
access_control_logger = AccessControlLogger()

# 创建脱敏日志记录器的便捷函数
def get_sanitized_logger(name: str) -> SanitizedLogger:
    """获取脱敏日志记录器"""
    return SanitizedLogger(name)
