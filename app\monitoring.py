"""
监控模块 - 性能监控和指标收集
"""

import time
import threading
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import json
import os
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RequestMetrics:
    """请求指标数据类"""
    timestamp: float
    method: str
    path: str
    status_code: int
    response_time: float
    client_id: str
    user_agent: str
    error_message: Optional[str] = None

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.lock = threading.Lock()

        # 请求指标
        self.request_count = defaultdict(int)
        self.response_times = defaultdict(deque)
        self.status_codes = defaultdict(int)
        self.error_count = defaultdict(int)

        # 客户端指标
        self.client_requests = defaultdict(int)
        self.client_errors = defaultdict(int)

        # 历史数据存储 - 修复缺失的属性
        self.max_history_size = 10000
        self.request_history = deque(maxlen=self.max_history_size)
        self.system_history = deque(maxlen=self.max_history_size)
        self.security_history = deque(maxlen=self.max_history_size)

        # 统计数据
        self.request_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.attack_counts = defaultdict(int)
        self.blocked_ips = set()

        self.stats = {
            'total_requests': 0,
            'total_errors': 0,
            'total_attacks': 0,
            'uptime_start': time.time(),
            'last_reset': time.time()
        }

        # 系统指标
        self.system_metrics = {
            'start_time': time.time(),
            'total_requests': 0,
            'total_errors': 0,
            'avg_response_time': 0.0,
            'peak_response_time': 0.0,
            'active_connections': 0
        }

        # 时间窗口配置
        self.window_size = 1000  # 保留最近1000个响应时间
        self.cleanup_interval = 300  # 5分钟清理一次
        self.last_cleanup = time.time()

        # 启动清理线程
        self.start_cleanup_thread()
    
    def record_request(self, method: str, path: str, status_code: int, 
                      response_time: float, client_id: str, user_agent: str,
                      error_message: Optional[str] = None):
        """记录请求指标"""
        with self.lock:
            # 基本请求统计
            endpoint = f"{method} {path}"
            self.request_count[endpoint] += 1
            self.status_codes[status_code] += 1
            
            # 响应时间统计
            self.response_times[endpoint].append(response_time)
            if len(self.response_times[endpoint]) > self.window_size:
                self.response_times[endpoint].popleft()
            
            # 错误统计
            if status_code >= 400:
                self.error_count[endpoint] += 1
                self.client_errors[client_id] += 1
            
            # 客户端统计
            self.client_requests[client_id] += 1
            
            # 系统指标更新
            self.system_metrics['total_requests'] += 1
            if status_code >= 400:
                self.system_metrics['total_errors'] += 1
            
            # 更新平均响应时间
            all_times = []
            for times in self.response_times.values():
                all_times.extend(times)
            
            if all_times:
                self.system_metrics['avg_response_time'] = sum(all_times) / len(all_times)
                self.system_metrics['peak_response_time'] = max(
                    self.system_metrics['peak_response_time'], 
                    max(all_times)
                )
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self.lock:
            now = time.time()
            uptime = now - self.system_metrics['start_time']
            
            # 计算错误率
            error_rate = 0.0
            if self.system_metrics['total_requests'] > 0:
                error_rate = (self.system_metrics['total_errors'] / 
                            self.system_metrics['total_requests']) * 100
            
            # 计算请求速率
            request_rate = 0.0
            if uptime > 0:
                request_rate = self.system_metrics['total_requests'] / uptime
            
            # 获取最活跃的端点
            top_endpoints = sorted(
                self.request_count.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
            
            # 获取最慢的端点
            slow_endpoints = []
            for endpoint, times in self.response_times.items():
                if times:
                    avg_time = sum(times) / len(times)
                    slow_endpoints.append((endpoint, avg_time))
            
            slow_endpoints.sort(key=lambda x: x[1], reverse=True)
            slow_endpoints = slow_endpoints[:10]
            
            return {
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': uptime,
                'system': {
                    'total_requests': self.system_metrics['total_requests'],
                    'total_errors': self.system_metrics['total_errors'],
                    'error_rate': error_rate,
                    'request_rate': request_rate,
                    'avg_response_time': self.system_metrics['avg_response_time'],
                    'peak_response_time': self.system_metrics['peak_response_time']
                },
                'endpoints': {
                    'top_active': top_endpoints,
                    'slowest': slow_endpoints
                }
            }
    
    def get_request_stats(self, time_window: int = 3600) -> Dict[str, Any]:
        """获取请求统计信息"""
        with self.lock:
            now = time.time()
            cutoff_time = now - time_window
            
            # 过滤时间窗口内的请求
            recent_requests = [r for r in self.request_history if r.timestamp > cutoff_time]
            
            if not recent_requests:
                return {
                    'total_requests': 0,
                    'error_rate': 0,
                    'avg_response_time': 0,
                    'requests_per_minute': 0,
                    'status_codes': {},
                    'top_endpoints': [],
                    'slow_requests': []
                }
            
            # 计算统计信息
            total_requests = len(recent_requests)
            error_requests = len([r for r in recent_requests if r.status_code >= 400])
            error_rate = (error_requests / total_requests) * 100 if total_requests > 0 else 0
            
            response_times = [r.response_time for r in recent_requests]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            requests_per_minute = (total_requests / time_window) * 60
            
            # 状态码分布
            status_codes = defaultdict(int)
            for request in recent_requests:
                status_codes[request.status_code] += 1
            
            # 热门端点
            endpoint_counts = defaultdict(int)
            for request in recent_requests:
                endpoint_counts[f"{request.method} {request.path}"] += 1
            
            top_endpoints = sorted(endpoint_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # 慢请求
            slow_requests = [
                {
                    'path': r.path,
                    'method': r.method,
                    'response_time': r.response_time,
                    'timestamp': r.timestamp
                }
                for r in recent_requests 
                if r.response_time > 5.0  # 超过5秒的请求
            ]
            slow_requests.sort(key=lambda x: x['response_time'], reverse=True)
            
            return {
                'total_requests': total_requests,
                'error_rate': round(error_rate, 2),
                'avg_response_time': round(avg_response_time, 3),
                'requests_per_minute': round(requests_per_minute, 2),
                'status_codes': dict(status_codes),
                'top_endpoints': top_endpoints[:5],
                'slow_requests': slow_requests[:10]
            }
    
    def get_security_stats(self, time_window: int = 3600) -> Dict[str, Any]:
        """获取安全统计信息"""
        with self.lock:
            now = time.time()
            cutoff_time = now - time_window
            
            # 计算时间窗口内的攻击统计
            recent_attacks = sum(1 for s in self.security_history if s.timestamp > cutoff_time)
            
            return {
                'total_attacks': self.stats['total_attacks'],
                'recent_attacks': recent_attacks,
                'blocked_ips_count': len(self.blocked_ips),
                'attack_types': dict(self.attack_counts),
                'attacks_per_hour': (recent_attacks / time_window) * 3600 if time_window > 0 else 0
            }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        with self.lock:
            if not self.system_history:
                return {
                    'cpu_percent': 0,
                    'memory_percent': 0,
                    'memory_used_mb': 0,
                    'disk_usage_percent': 0,
                    'active_connections': 0
                }
            
            latest = self.system_history[-1]
            return asdict(latest)
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        request_stats = self.get_request_stats(300)  # 最近5分钟
        system_stats = self.get_system_stats()
        security_stats = self.get_security_stats(300)
        
        # 健康检查逻辑
        health_issues = []
        
        # 检查错误率
        if request_stats['error_rate'] > 10:
            health_issues.append(f"高错误率: {request_stats['error_rate']}%")
        
        # 检查响应时间
        if request_stats['avg_response_time'] > 5:
            health_issues.append(f"响应时间过长: {request_stats['avg_response_time']}s")
        
        # 检查系统资源
        if system_stats['cpu_percent'] > 80:
            health_issues.append(f"CPU使用率过高: {system_stats['cpu_percent']}%")
        
        if system_stats['memory_percent'] > 85:
            health_issues.append(f"内存使用率过高: {system_stats['memory_percent']}%")
        
        # 检查攻击频率
        if security_stats['attacks_per_hour'] > 100:
            health_issues.append(f"攻击频率过高: {security_stats['attacks_per_hour']}/小时")
        
        # 确定健康状态
        if not health_issues:
            status = "healthy"
        elif len(health_issues) <= 2:
            status = "degraded"
        else:
            status = "unhealthy"
        
        uptime_seconds = int(time.time() - self.stats['uptime_start'])
        uptime_formatted = self._format_uptime(uptime_seconds)
        
        return {
            'status': status,
            'issues': health_issues,
            'uptime_seconds': uptime_seconds,
            'uptime_formatted': uptime_formatted,
            'last_check': datetime.now().isoformat(),
            'stats_summary': {
                'total_requests': self.stats['total_requests'],
                'total_errors': self.stats['total_errors'],
                'total_attacks': self.stats['total_attacks'],
                'error_rate': request_stats['error_rate'],
                'avg_response_time': request_stats['avg_response_time']
            }
        }
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """格式化运行时间"""
        uptime = timedelta(seconds=int(uptime_seconds))
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}天 {hours}小时 {minutes}分钟"
        elif hours > 0:
            return f"{hours}小时 {minutes}分钟"
        else:
            return f"{minutes}分钟 {seconds}秒"
    
    def _background_monitoring(self):
        """后台监控线程"""
        while True:
            try:
                # 每30秒记录一次系统指标
                self.record_system_metrics()
                
                # 每5分钟清理过期数据
                if int(time.time()) % 300 == 0:
                    self._cleanup_old_data()
                
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"后台监控异常: {e}")
                time.sleep(60)  # 出错后等待1分钟
    
    def start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    self._cleanup_old_data()
                except Exception as e:
                    logger.error(f"清理线程异常: {e}")
                    time.sleep(60)
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("清理线程已启动")
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        with self.lock:
            now = time.time()
            cutoff_time = now - 86400  # 保留24小时数据
            
            # 清理请求历史
            self.request_history = deque(
                [r for r in self.request_history if r.timestamp > cutoff_time],
                maxlen=self.max_history_size
            )
            
            # 清理系统历史
            self.system_history = deque(
                [s for s in self.system_history if s.timestamp > cutoff_time],
                maxlen=self.max_history_size
            )
            
            # 清理安全历史
            self.security_history = deque(
                [s for s in self.security_history if s.timestamp > cutoff_time],
                maxlen=self.max_history_size
            )
            
        logger.info("清理过期监控数据完成")
    
    def export_metrics(self, format_type: str = "json") -> str:
        """导出指标数据"""
        with self.lock:
            data = {
                'timestamp': datetime.now().isoformat(),
                'request_stats': self.get_request_stats(),
                'security_stats': self.get_security_stats(),
                'system_stats': self.get_system_stats(),
                'health_status': self.get_health_status()
            }
            
            if format_type == "json":
                return json.dumps(data, indent=2, ensure_ascii=False)
            else:
                return str(data)
    
    def reset_stats(self):
        """重置统计数据"""
        with self.lock:
            self.request_history.clear()
            self.request_counts.clear()
            self.error_counts.clear()
            self.response_times.clear()
            self.system_history.clear()
            self.security_history.clear()
            self.attack_counts.clear()
            self.blocked_ips.clear()
            
            self.stats = {
                'total_requests': 0,
                'total_errors': 0,
                'total_attacks': 0,
                'uptime_start': time.time(),
                'last_reset': time.time()
            }
            
        logger.info("监控统计数据已重置")

# 创建全局指标收集器实例
metrics_collector = MetricsCollector()