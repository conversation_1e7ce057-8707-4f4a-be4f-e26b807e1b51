#!/usr/bin/env python3
"""
AES加密数据分析工具
"""

import base64
import hashlib

def analyze_encrypted_data():
    """分析加密数据结构"""
    
    # 你提供的加密数据
    encrypted_link = "aes:VvI4dWPMPy+D0umION1qgXttWYBq1tdkF6jkegt2heQHWK/N5P2TX1Z8rWS+3RyV7nOZNKyDVbDXZuKnvUZCb+1dJTsSaHFLhZa1RRqFExQmeaHp"
    
    print("🔐 AES加密数据分析")
    print("=" * 50)
    
    # 移除前缀
    base64_data = encrypted_link[4:]  # 移除 "aes:"
    print(f"Base64数据: {base64_data}")
    print(f"Base64长度: {len(base64_data)} 字符")
    
    # Base64解码
    try:
        encrypted_bytes = base64.b64decode(base64_data)
        print(f"解码后字节长度: {len(encrypted_bytes)} 字节")
        
        # 分离nonce和密文
        nonce = encrypted_bytes[:12]
        ciphertext = encrypted_bytes[12:]
        
        print(f"\n📊 数据结构分析:")
        print(f"Nonce (前12字节): {nonce.hex()}")
        print(f"Nonce长度: {len(nonce)} 字节")
        print(f"密文 (剩余字节): {ciphertext.hex()}")
        print(f"密文长度: {len(ciphertext)} 字节")
        
        # 分析密钥生成过程
        print(f"\n🔑 密钥生成分析:")
        key_material = "your-encryption-key-32-chars-long"
        print(f"原始密钥材料: {key_material}")
        
        # 生成实际使用的密钥
        encryption_key = hashlib.sha256(key_material.encode('utf-8')).digest()[:32]
        print(f"SHA256哈希后的密钥: {encryption_key.hex()}")
        print(f"密钥长度: {len(encryption_key)} 字节 (AES-256需要32字节)")
        
        # 安全性分析
        print(f"\n🛡️ 安全性分析:")
        print(f"✅ 使用AES-256-GCM算法 (军用级加密)")
        print(f"✅ 每次加密都有随机nonce (防止相同明文产生相同密文)")
        print(f"✅ GCM模式提供认证加密 (防篡改)")
        print(f"⚠️ 密钥固定在配置中 (可通过环境变量更改)")
        
        # 破解难度分析
        print(f"\n🤖 爬虫破解难度:")
        print(f"❌ 暴力破解: 2^256 种可能 (宇宙热寂前无法破解)")
        print(f"❌ 密文分析: GCM模式抗分析攻击")
        print(f"⚠️ 密钥泄露: 如果配置文件泄露，可被破解")
        print(f"⚠️ 代码逆向: 如果服务器代码泄露，可被破解")
        
        return True
        
    except Exception as e:
        print(f"❌ 解码失败: {e}")
        return False

if __name__ == "__main__":
    analyze_encrypted_data()
