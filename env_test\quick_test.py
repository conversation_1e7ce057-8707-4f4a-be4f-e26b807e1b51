#!/usr/bin/env python3
"""
快速测试搜索验证修复
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'app'))

from security import SecurityValidator

def main():
    print("🔍 快速测试搜索验证修复")
    print("=" * 40)
    
    # 测试问题关键词
    test_keyword = "利剑·玫瑰"
    
    print(f"测试关键词: '{test_keyword}'")
    
    is_valid, error_msg = SecurityValidator.validate_search_keyword(test_keyword)
    
    if is_valid:
        print("✅ 验证通过！")
        print("🎉 搜索验证问题已修复")
        
        # 测试更多包含间隔号的电影名
        more_tests = [
            "哈利·波特",
            "蜘蛛侠：英雄归来", 
            "X战警：逆转未来",
            "复仇者联盟：无限战争"
        ]
        
        print("\n其他电影名测试:")
        for movie in more_tests:
            valid, msg = SecurityValidator.validate_search_keyword(movie)
            status = "✅" if valid else "❌"
            print(f"{status} {movie}")
            if not valid:
                print(f"   错误: {msg}")
        
        return True
    else:
        print(f"❌ 验证失败: {error_msg}")
        print("⚠️ 问题仍未修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
