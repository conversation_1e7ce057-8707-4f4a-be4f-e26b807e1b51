/**
 * 电影搜索应用 - 前端 JavaScript
 * 基于参考项目的现代化设计重构
 */

// 全局应用实例
let app;

class MovieSearchApp {
    constructor() {
        this.apiBase = '/api';
        this.searchInput = document.getElementById('search-input');
        this.searchForm = document.getElementById('search-form');
        this.searchBtn1 = document.getElementById('search-btn-1');
        this.searchBtn2 = document.getElementById('search-btn-2');
        this.hotMoviesContainer = document.getElementById('hot-movies');
        this.resultsSection = document.getElementById('results-section');
        this.searchResults = document.getElementById('search-results');
        this.resultsCount = document.getElementById('results-count');
        this.searchStats = document.getElementById('search-stats');
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.notification = document.getElementById('notification');
        this.hotUpdateTime = document.getElementById('hot-update-time');
        this.inputCounter = document.getElementById('input-counter');
        this.emptyState = document.getElementById('empty-state');

        // 搜索状态管理
        this.activeSearchType = 'wps'; // 'wps' | 'cloudsave'
        this.currentSearchTerm = '';
        this.allSearchResults = [];
        this.isSearching = false;

        // 会话令牌（动态获取）
        this.sessionToken = null;
        this.tokenExpiry = 0;

        this.init();
    }

    // 生成浏览器指纹
    generateBrowserFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Browser fingerprint', 2, 2);

        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL(),
            navigator.hardwareConcurrency || 0
        ].join('|');

        // 简单哈希
        let hash = 0;
        for (let i = 0; i < fingerprint.length; i++) {
            const char = fingerprint.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(16);
    }

    // 获取会话令牌
    async getSessionToken() {
        // 检查令牌是否过期
        if (this.sessionToken && Date.now() < this.tokenExpiry) {
            return this.sessionToken;
        }

        try {
            const fingerprint = this.generateBrowserFingerprint();
            const response = await fetch('/api/session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fingerprint: fingerprint,
                    timestamp: Date.now()
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.sessionToken = data.token;
                this.tokenExpiry = Date.now() + (data.expires_in * 1000);
                return this.sessionToken;
            }
        } catch (error) {
            console.error('获取会话令牌失败:', error);
        }

        return null;
    }

    // 创建认证请求头
    async createAuthHeaders(method, path, body = '') {
        const token = await this.getSessionToken();
        const fingerprint = this.generateBrowserFingerprint();
        const timestamp = Date.now();

        return {
            'X-Session-Token': token,
            'X-Browser-Fingerprint': fingerprint,
            'X-Timestamp': timestamp,
            'Content-Type': 'application/json'
        };
    }

    // 解密链接
    async decryptLink(encryptedLink) {
        // 如果不是加密链接，直接返回
        if (!encryptedLink.startsWith('aes:')) {
            return encryptedLink;
        }

        try {
            const authHeaders = await this.createAuthHeaders('POST', '/api/decrypt-link', JSON.stringify({encrypted_link: encryptedLink}));

            const response = await fetch('/api/decrypt-link', {
                method: 'POST',
                headers: authHeaders,
                body: JSON.stringify({
                    encrypted_link: encryptedLink
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.decrypted_link || encryptedLink;
            } else {
                console.error('解密链接失败:', response.status);
                return encryptedLink;
            }
        } catch (error) {
            console.error('解密链接异常:', error);
            return encryptedLink;
        }
    }

    // 处理链接点击
    async handleLinkClick(encryptedLink, event) {
        event.preventDefault(); // 阻止默认行为

        try {
            // 解密链接（后台进行，用户无感知）
            const decryptedLink = await this.decryptLink(encryptedLink);

            // 打开解密后的链接
            if (decryptedLink && decryptedLink !== encryptedLink) {
                window.open(decryptedLink, '_blank', 'noopener,noreferrer');
            } else {
                this.showNotification('错误', '链接暂时无法访问，请稍后重试', 'error');
            }

        } catch (error) {
            console.error('处理链接点击异常:', error);
            this.showNotification('错误', '链接暂时无法访问，请稍后重试', 'error');
        }
    }
    
    init() {
        this.bindEvents();
        this.loadHotMovies();
        this.setupInputCounter();
    }
    
    bindEvents() {
        // 搜索按钮点击事件
        this.searchBtn1.addEventListener('click', () => {
            this.setActiveSearchType('wps');
            this.searchMovies();
        });

        this.searchBtn2.addEventListener('click', () => {
            this.setActiveSearchType('cloudsave');
            this.searchMovies();
        });

        // 搜索表单提交（回车键）
        this.searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.searchMovies();
        });

        // 输入框字符计数
        this.searchInput.addEventListener('input', () => {
            this.updateInputCounter();
        });

        // 通知关闭
        const notificationClose = document.getElementById('notification-close');
        if (notificationClose) {
            notificationClose.addEventListener('click', () => {
                this.hideNotification();
            });
        }
    }

    setActiveSearchType(searchType) {
        this.activeSearchType = searchType;

        // 更新按钮状态
        if (searchType === 'wps') {
            this.searchBtn1.classList.add('active');
            this.searchBtn2.classList.remove('active');
        } else {
            this.searchBtn1.classList.remove('active');
            this.searchBtn2.classList.add('active');
        }
    }

    setupInputCounter() {
        this.updateInputCounter();
    }
    
    updateInputCounter() {
        const length = this.searchInput.value.length;
        this.inputCounter.textContent = `${length}/50`;
        
        if (length > 40) {
            this.inputCounter.style.color = '#EF4444';
        } else if (length > 30) {
            this.inputCounter.style.color = '#F59E0B';
        } else {
            this.inputCounter.style.color = '#9CA3AF';
        }
    }
    
    async loadHotMovies() {
        try {
            const timestamp = new Date().getTime();
            const response = await fetch(`${this.apiBase}/hot-movies?t=${timestamp}`);
            const data = await response.json();
            
            if (data.success && data.data) {
                this.renderHotMovies(data.data);
                if (data.update_time) {
                    this.hotUpdateTime.textContent = `更新时间: ${new Date(data.update_time).toLocaleString()}`;
                }
            } else {
                this.renderDefaultHotMovies();
            }
        } catch (error) {
            console.error('获取热门电影失败:', error);
            this.renderDefaultHotMovies();
        }
    }
    
    renderHotMovies(movies) {
        if (!movies || movies.length === 0) {
            this.renderDefaultHotMovies();
            return;
        }
        
        this.hotMoviesContainer.innerHTML = movies.map((movie, index) => {
            const isHot = index < 3;
            return `
                <button 
                    class="hot-movie-btn ${isHot ? 'hot' : 'normal'}"
                    onclick="app.searchHotMovie('${movie.replace(/'/g, "\\'")}')"
                >
                    ${movie}
                </button>
            `;
        }).join('');
    }
    
    renderDefaultHotMovies() {
        const defaultMovies = ['流浪地球3', '热辣滚烫', '独行月球2', '熊出没', '消失的她'];
        this.renderHotMovies(defaultMovies);
    }
    
    searchHotMovie(movieName) {
        this.searchInput.value = movieName;
        this.updateInputCounter();
        // 热门标签点击默认使用搜索一
        this.setActiveSearchType('wps');
        this.searchMovies();
    }
    
    async searchMovies() {
        const keyword = this.searchInput.value.trim();
        if (!keyword) {
            this.showNotification('提示', '请输入搜索关键词', 'error');
            return;
        }
        
        if (this.isSearching) return;
        
        this.isSearching = true;
        this.currentSearchTerm = keyword;
        this.showLoading();
        this.updateSearchButton(true);
        
        try {
            // 根据搜索类型选择不同的API端点
            const endpoint = this.activeSearchType === 'wps' ? '/search' : '/search-cloudsave';
            const requestBody = JSON.stringify({ keyword });

            // 创建认证请求头
            const authHeaders = await this.createAuthHeaders('POST', `${this.apiBase}${endpoint}`, requestBody);

            const response = await fetch(`${this.apiBase}${endpoint}`, {
                method: 'POST',
                headers: authHeaders,
                body: requestBody
            });

            console.log('搜索响应状态码:', response.status);
            console.log('搜索响应状态文本:', response.statusText);

            // 专门处理429状态码（频率限制）
            if (response.status === 429) {
                console.log('检测到429状态码，处理频率限制');

                const data = await response.json();
                console.log('429响应数据:', data);

                const errorMessage = data.error?.message || '请求过于频繁';
                console.log('频率限制错误信息:', errorMessage);

                // 提取剩余时间（如果有的话）
                const timeMatch = errorMessage.match(/(\d+)秒/);
                const remainingTime = timeMatch ? timeMatch[1] : null;
                console.log('提取的剩余时间:', remainingTime);

                let userMessage = '搜索过于频繁，请稍后再试';
                if (remainingTime) {
                    const minutes = Math.ceil(remainingTime / 60);
                    userMessage = `搜索过于频繁，请等待 ${minutes} 分钟后再试`;
                }

                console.log('显示用户消息:', userMessage);

                // 直接在页面顶部显示醒目的提示
                this.showRateLimitWarning(userMessage);

                this.allSearchResults = [];
                this.renderResults();
                return;
            }

            // 处理其他状态码的响应
            const data = await response.json();
            console.log('搜索响应数据:', data);

            // 处理成功响应
            if (data.success) {
                if (this.activeSearchType === 'wps') {
                    this.allSearchResults = data.data || [];
                } else {
                    // CloudSave结果需要转换格式
                    this.allSearchResults = this.formatCloudsaveResults(data.data || {});
                }
                this.renderResults();

                this.showNotification('成功', `找到 ${this.allSearchResults.length} 个结果`, 'success');
            } else {
                // 处理其他错误
                throw new Error(data.error?.message || '搜索失败');
            }
        } catch (error) {
            console.error('搜索失败:', error);
            console.log('错误类型:', typeof error);
            console.log('错误详情:', error);
            this.showNotification('错误', error.message || '搜索失败，请稍后重试', 'error');
            this.allSearchResults = [];
            this.renderResults();
        } finally {
            this.isSearching = false;
            this.hideLoading();
            this.updateSearchButton(false);
        }
    }

    formatCloudsaveResults(cloudsaveData) {
        const results = [];

        // 云盘类型映射
        const cloudTypeMap = {
            'quark': { name: '夸克网盘', icon: '🟢', color: 'green' },
            'baidu': { name: '百度网盘', icon: '🔵', color: 'blue' },
            'aliyun': { name: '阿里云盘', icon: '🟡', color: 'yellow' },
            'tianyi': { name: '天翼云盘', icon: '🟠', color: 'orange' },
            'xunlei': { name: '迅雷网盘', icon: '🔴', color: 'red' },
            'uc': { name: 'UC网盘', icon: '🟣', color: 'purple' },
            'pan123': { name: '123网盘', icon: '⚫', color: 'gray' },
            '115': { name: '115网盘', icon: '🟤', color: 'brown' },
            'lanzou': { name: '蓝奏云', icon: '🔷', color: 'cyan' },
            'weiyun': { name: '微云', icon: '🟢', color: 'green' },
            'caiyun': { name: '彩云', icon: '🌈', color: 'rainbow' }
        };

        for (const [cloudType, items] of Object.entries(cloudsaveData)) {
            const cloudInfo = cloudTypeMap[cloudType] || { name: cloudType, icon: '💾', color: 'gray' };

            items.forEach(item => {
                results.push({
                    title: item.title,
                    links: [{
                        type: cloudType,
                        url: item.link,
                        video_count: item.video_count || null
                    }],
                    video_count_total: item.video_count || 0,
                    episode: item.episode,
                    cloudType: cloudType,
                    cloudInfo: cloudInfo,
                    pubDate: item.pubDate,
                    channel: item.channel
                });
            });
        }

        return results;
    }

    sortByVideoCount(results) {
        return results.sort((a, b) => {
            const aVideoCount = a.video_count_total || 0;
            const bVideoCount = b.video_count_total || 0;
            
            // 有视频数量的优先
            if (aVideoCount > 0 && bVideoCount === 0) return -1;
            if (bVideoCount > 0 && aVideoCount === 0) return 1;
            
            // 都有视频数量时，按数量排序
            if (aVideoCount !== bVideoCount) {
                return bVideoCount - aVideoCount;
            }
            
            // 视频数量相同时，按链接数量排序
            const aLinkCount = (a.links || []).length;
            const bLinkCount = (b.links || []).length;
            return bLinkCount - aLinkCount;
        });
    }
    
    renderResults() {
        // 按视频数量排序
        const sortedResults = this.sortByVideoCount([...this.allSearchResults]);

        // 过滤掉没有有效链接的结果
        const validResults = sortedResults.filter(result => {
            if (!result.links || result.links.length === 0) {
                return false;
            }

            // 检查是否有有效的链接
            const validLinks = result.links.filter(link => {
                if (link.type === 'quark') {
                    const videoCount = link.video_count;
                    return videoCount !== null && videoCount !== undefined && videoCount > 0;
                }
                return true; // 百度链接保持原样
            });

            return validLinks.length > 0;
        });

        if (validResults.length === 0) {
            this.resultsSection.classList.add('hidden');
            this.emptyState.classList.remove('hidden');
            return;
        }

        this.emptyState.classList.add('hidden');
        this.resultsSection.classList.remove('hidden');

        // 更新结果计数
        this.resultsCount.textContent = `(${validResults.length})`;

        // 渲染结果列表
        this.searchResults.innerHTML = validResults.map((result, index) => {
            return this.renderResultCard(result, index);
        }).join('');

        // 添加淡入动画
        this.searchResults.classList.add('fade-in');
    }
    
    renderResultCard(result, index) {
        // 判断是否为CloudSave结果
        if (result.cloudType && result.cloudInfo) {
            return this.renderCloudsaveCard(result, index);
        }

        // 原有的WPS搜索结果渲染逻辑
        const hasLinks = result.links && result.links.length > 0;
        const videoCount = result.video_count_total || 0;

        // 检查是否有有效的夸克链接
        const hasValidQuarkLinks = result.links && result.links.some(link => {
            if (link.type === 'quark') {
                const linkVideoCount = link.video_count;
                return linkVideoCount !== null && linkVideoCount !== undefined && linkVideoCount > 0;
            }
            return false;
        });

        return `
            <div class="result-card slide-up" style="animation-delay: ${index * 0.1}s">
                <div class="result-title">${this.escapeHtml(result.title)}</div>

                <div class="result-stats">
                    <div>总链接: ${(result.links || []).length}</div>
                    ${hasValidQuarkLinks ? `<div class="video-count-display">夸克视频文件: ${videoCount > 0 ? videoCount : '未知'}</div>` : ''}
                </div>

                ${hasLinks ? this.renderLinks(result.links) : this.renderNoLinks()}
            </div>
        `;
    }

    renderCloudsaveCard(result, index) {
        const cloudInfo = result.cloudInfo;
        const videoCount = result.video_count_total || 0;

        return `
            <div class="result-card slide-up" style="animation-delay: ${index * 0.1}s">
                <div class="cloudsave-header">
                    <span class="cloud-icon">${cloudInfo.icon}</span>
                    <span class="cloud-name">${cloudInfo.name}</span>
                </div>

                <div class="result-title">${this.escapeHtml(result.title)}</div>

                <div class="cloudsave-info-row">
                    <div class="cloudsave-stats">
                        ${result.cloudType === 'quark' && videoCount > 0 ? `<span>夸克视频文件: ${videoCount}</span>` : ''}
                        <span>链接检测有效</span>
                    </div>
                    <div class="cloudsave-actions">
                        <button class="link-btn primary" onclick="app.handleLinkClick('${result.links[0].url}', event)">
                            打开链接
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderLinks(links) {
        // 过滤掉夸克链接中视频文件数量为null、undefined或0的链接
        const validLinks = links.filter(link => {
            if (link.type === 'quark') {
                const videoCount = link.video_count;
                return videoCount !== null && videoCount !== undefined && videoCount > 0;
            }
            return true; // 百度链接保持原样
        });

        if (validLinks.length === 0) {
            return this.renderNoLinks();
        }

        return `
            <div class="result-links">
                ${validLinks.map(link => this.renderLinkItem(link)).join('')}
            </div>
        `;
    }
    
    renderLinkItem(link) {
        const linkType = link.type === 'baidu' ? 'baidu' : 'quark';
        const linkTypeName = link.type === 'baidu' ? '百度网盘' : '夸克网盘';

        return `
            <div class="link-item">
                <div class="link-info">
                    <div>
                        <span class="link-type ${linkType}">${linkTypeName}</span>
                        <span class="link-name">链接检测有效</span>
                    </div>
                </div>
                <div class="link-actions">
                    <button class="link-btn primary" onclick="app.handleLinkClick('${link.url}', event)">
                        打开链接
                    </button>
                </div>
            </div>
        `;
    }
    
    renderNoLinks() {
        return `
            <div class="result-links">
                <div class="text-center py-4 text-gray-500">
                    <div class="text-2xl mb-2">🔗</div>
                    <div>暂无有效链接</div>
                </div>
            </div>
        `;
    }
    

    

    
    showLoading() {
        this.loadingOverlay.classList.remove('hidden');
    }
    
    hideLoading() {
        this.loadingOverlay.classList.add('hidden');
    }
    
    updateSearchButton(isLoading) {
        const activeBtn = this.activeSearchType === 'wps' ? this.searchBtn1 : this.searchBtn2;
        const btnText = activeBtn.querySelector('.btn-text');
        const btnIcon = activeBtn.querySelector('.btn-icon');

        if (isLoading) {
            btnText.textContent = '搜索中...';
            btnIcon.textContent = '⏳';
            this.searchBtn1.disabled = true;
            this.searchBtn2.disabled = true;
        } else {
            // 恢复按钮文本
            this.searchBtn1.querySelector('.btn-text').textContent = '搜索一';
            this.searchBtn1.querySelector('.btn-icon').textContent = '🔍';
            this.searchBtn2.querySelector('.btn-text').textContent = '搜索二';
            this.searchBtn2.querySelector('.btn-icon').textContent = '☁️';

            this.searchBtn1.disabled = false;
            this.searchBtn2.disabled = false;
        }
    }
    
    showNotification(title, message, type = 'success') {
        console.log('showNotification 被调用:', { title, message, type });

        const messageEl = document.getElementById('notification-message');
        const successIcon = document.getElementById('notification-icon-success');
        const warningIcon = document.getElementById('notification-icon-warning');
        const errorIcon = document.getElementById('notification-icon-error');

        console.log('通知元素检查:', {
            notification: this.notification,
            messageEl: messageEl,
            successIcon: successIcon,
            warningIcon: warningIcon,
            errorIcon: errorIcon
        });

        if (!this.notification || !messageEl) {
            console.error('通知元素未找到!');
            return;
        }

        // 简化消息，只显示主要内容
        let displayMessage = message;
        if (type === 'success' && message.includes('找到')) {
            // 优化成功消息显示
            displayMessage = message.replace('找到 ', '').replace(' 个结果', '个结果');
        }

        messageEl.textContent = displayMessage;
        console.log('设置消息文本:', displayMessage);

        // 设置通知样式
        this.notification.className = `fixed top-4 right-4 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 p-4 z-50 transform transition-all duration-300 ease-in-out ${type}`;
        console.log('设置通知样式:', this.notification.className);

        // 显示对应图标
        successIcon.classList.add('hidden');
        warningIcon.classList.add('hidden');
        errorIcon.classList.add('hidden');

        if (type === 'success') {
            successIcon.classList.remove('hidden');
        } else if (type === 'warning') {
            warningIcon.classList.remove('hidden');
            console.log('显示警告图标');
        } else {
            errorIcon.classList.remove('hidden');
        }

        // 显示通知
        this.notification.classList.remove('hidden', 'hide');
        this.notification.classList.add('show');
        console.log('显示通知，当前类名:', this.notification.className);

        // 自动隐藏（警告类型显示更长时间）
        const hideDelay = type === 'warning' ? 5000 : 3000;
        console.log(`设置自动隐藏，延迟: ${hideDelay}ms`);
        setTimeout(() => {
            this.hideNotification();
        }, hideDelay);
    }

    // 显示频率限制警告（简单直接的方式）
    showRateLimitWarning(message) {
        // 移除之前的警告（如果有的话）
        const existingWarning = document.getElementById('rate-limit-warning');
        if (existingWarning) {
            existingWarning.remove();
        }

        // 创建警告元素
        const warning = document.createElement('div');
        warning.id = 'rate-limit-warning';
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            max-width: 400px;
            text-align: center;
            animation: slideDown 0.3s ease-out;
        `;
        warning.textContent = message;

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
            @keyframes slideUp {
                from { opacity: 1; transform: translateX(-50%) translateY(0); }
                to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
            }
        `;
        document.head.appendChild(style);

        // 添加到页面
        document.body.appendChild(warning);

        // 5秒后自动消失
        setTimeout(() => {
            warning.style.animation = 'slideUp 0.3s ease-out';
            setTimeout(() => {
                if (warning.parentNode) {
                    warning.remove();
                }
            }, 300);
        }, 5000);
    }

    hideNotification() {
        this.notification.classList.remove('show');
        this.notification.classList.add('hide');
        
        // 等待动画完成后隐藏
        setTimeout(() => {
            this.notification.classList.add('hidden');
            this.notification.classList.remove('hide');
        }, 300);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new MovieSearchApp();
});

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});