#!/usr/bin/env python3
"""
测试解密API
"""

import requests
import json

def test_decrypt_api():
    """测试解密API"""
    
    # 你提供的加密链接
    encrypted_link = "aes:VvI4dWPMPy+D0umION1qgXttWYBq1tdkF6jkegt2heQHWK/N5P2TX1Z8rWS+3RyV7nOZNKyDVbDXZuKnvUZCb+1dJTsSaHFLhZa1RRqFExQmeaHp"
    
    print("🔓 测试解密API")
    print("=" * 50)
    print(f"加密链接: {encrypted_link}")
    
    try:
        # 调用解密API
        response = requests.post(
            "http://localhost:8080/api/decrypt-link",
            headers={
                "Content-Type": "application/json",
                "X-Session-Token": "test_token",  # 这里需要有效的会话令牌
                "X-Browser-Fingerprint": "test_fingerprint"
            },
            json={
                "encrypted_link": encrypted_link
            }
        )
        
        print(f"\n📊 API响应:")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 解密成功!")
            print(f"原始链接: {data.get('decrypted_link', '未知')}")
        else:
            print(f"❌ 解密失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_decrypt_api()
