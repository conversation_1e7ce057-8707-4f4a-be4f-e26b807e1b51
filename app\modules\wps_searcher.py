"""
WPS文档链接提取模块 - 从备份文件复制的完整实现
"""

import requests
import re
import json
import logging
import time
from typing import Dict, List, Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
try:
    from .link_validator import validate_link
    from .config import DOCUMENT_URLS, DEFAULT_COOKIES, DEFAULT_HEADERS
except ImportError:
    try:
        from link_validator import validate_link
        from config import DOCUMENT_URLS, DEFAULT_COOKIES, DEFAULT_HEADERS
    except ImportError:
        # 如果无法导入配置，使用默认值
        DOCUMENT_URLS = []
        DEFAULT_COOKIES = {}
        DEFAULT_HEADERS = {}

logger = logging.getLogger(__name__)

class ContentInfo:
    """内容信息数据类"""

    def __init__(self, title: str = "", baidu_links: List[str] = None,
                 quark_links: List[str] = None, extract_code: str = "", source_url: str = ""):
        self.title = title
        self.baidu_links = baidu_links or []
        self.quark_links = quark_links or []
        self.extract_code = extract_code
        self.source_url = source_url
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'title': self.title,
            'baidu_links': self.baidu_links,
            'quark_links': self.quark_links,
            'extract_code': self.extract_code,
            'source_url': self.source_url
        }

class WPSContentSearcher:
    """WPS文档内容搜索器"""

    def __init__(self, document_urls: Union[str, List[str]] = None):
        if document_urls is None:
            document_urls = DOCUMENT_URLS
            
        if isinstance(document_urls, str):
            self.document_urls = [document_urls]
        else:
            self.document_urls = document_urls

        self.cookies = DEFAULT_COOKIES
        self.headers = DEFAULT_HEADERS

    def _format_keyword(self, keyword: str) -> str:
        """格式化关键词"""
        if not keyword:
            return ""
        keyword = keyword.strip()
        keyword = re.sub(r'\s+', ' ', keyword)
        keyword = keyword.replace("-", "")
        return keyword
    
    def _get_document_data(self, url: str) -> Optional[Dict]:
        """获取文档数据"""
        try:
            response = requests.get(url, headers=self.headers, cookies=self.cookies, timeout=10)
            response.raise_for_status()

            html_content = response.text
            pattern = r'window\.__WPSENV__\.open_data_body\s*=\s*({.*?});'
            match = re.search(pattern, html_content, re.DOTALL)

            if not match:
                return None

            open_data_body_str = match.group(1)
            open_data_body = json.loads(open_data_body_str)
            return open_data_body.get('content', {})

        except Exception as e:
            logger.error(f"获取文档数据失败: {url} - {e}")
            return None

    def _extract_text_from_content(self, content_list: List) -> List[str]:
        """从content列表中提取文本"""
        texts = []
        for item in content_list:
            if isinstance(item, dict):
                if item.get('type') == 'text':
                    texts.append(item.get('text', ''))
                elif 'content' in item and isinstance(item['content'], list):
                    texts.extend(self._extract_text_from_content(item['content']))
        return texts

    def _extract_links_from_content(self, content_list: List) -> List[Dict]:
        """从content列表中提取链接"""
        links = []
        for item in content_list:
            if isinstance(item, dict):
                if item.get('type') == 'text' and 'marks' in item:
                    for mark in item.get('marks', []):
                        if mark.get('type') == 'link':
                            href = mark.get('attrs', {}).get('href', '')
                            if href:
                                link_text = item.get('text', '')
                                links.append({
                                    'url': href,
                                    'text': link_text,
                                    'type': 'baidu' if 'baidu.com' in href else 'quark' if 'quark.cn' in href else 'other'
                                })
                elif 'content' in item and isinstance(item['content'], list):
                    links.extend(self._extract_links_from_content(item['content']))
        return links
    
    def _find_content_links_precise(self, keyword: str, logic_block: Dict, source_url: str = "") -> List[ContentInfo]:
        """精确查找包含关键词的内容及其相关链接"""
        block_content = logic_block.get('content', [])
        all_results = []

        # 找到所有包含关键词的tile索引
        target_tile_indices = []
        for i, block_tile in enumerate(block_content):
            if block_tile.get('type') == 'block_tile':
                tile_content = block_tile.get('content', [])
                texts = self._extract_text_from_content(tile_content)
                tile_text = ' '.join(texts)
                formatted_tile_text = tile_text.replace("-", "")

                if keyword.lower() in formatted_tile_text.lower():
                    target_tile_indices.append(i)

        if not target_tile_indices:
            return []

        # 为每个匹配的tile处理
        for target_tile_index in target_tile_indices:
            content_info = self._process_single_tile(block_content, target_tile_index, keyword, source_url)
            if content_info:
                all_results.append(content_info)

        return self._remove_duplicate_results(all_results)

    def _process_single_tile(self, block_content: List, target_tile_index: int, keyword: str, source_url: str) -> Optional[ContentInfo]:
        """处理单个匹配的tile"""
        target_tile = block_content[target_tile_index]
        target_tile_content = target_tile.get('content', [])
        title_texts = self._extract_text_from_content(target_tile_content)
        target_tile_links = self._extract_links_from_content(target_tile_content)

        content_info = ContentInfo()
        content_info.title = ' '.join(title_texts).strip().replace("-", "")
        content_info.source_url = source_url

        # 策略1: 检查目标tile是否直接包含链接
        if target_tile_links:
            for link in target_tile_links:
                if link['type'] == 'baidu':
                    content_info.baidu_links.append(link['url'])
                elif link['type'] == 'quark':
                    content_info.quark_links.append(link['url'])

            content_info.title = self._clean_title_from_links(content_info.title, target_tile_links)

            if content_info.baidu_links or content_info.quark_links:
                return content_info

        # 策略2: 在后续tile中查找链接
        max_search_range = min(target_tile_index + 5, len(block_content))

        for i in range(target_tile_index + 1, max_search_range):
            if i < len(block_content):
                tile = block_content[i]
                if tile.get('type') == 'block_tile':
                    tile_content = tile.get('content', [])
                    texts = self._extract_text_from_content(tile_content)
                    links = self._extract_links_from_content(tile_content)
                    tile_text = ' '.join(texts).strip()

                    if self._is_new_content_title(tile_text, keyword):
                        break

                    if links:
                        for link in links:
                            if link['type'] == 'baidu':
                                content_info.baidu_links.append(link['url'])
                            elif link['type'] == 'quark':
                                content_info.quark_links.append(link['url'])

        return content_info if (content_info.baidu_links or content_info.quark_links) else None

    def _remove_duplicate_results(self, results: List[ContentInfo]) -> List[ContentInfo]:
        """去除重复的搜索结果"""
        if not results:
            return results

        unique_results = []
        seen_combinations = set()

        for result in results:
            baidu_links_str = '|'.join(sorted(result.baidu_links))
            quark_links_str = '|'.join(sorted(result.quark_links))
            combination_key = f"{result.title}||{baidu_links_str}||{quark_links_str}"

            if combination_key not in seen_combinations:
                seen_combinations.add(combination_key)
                unique_results.append(result)

        return unique_results

    def _clean_title_from_links(self, title: str, links: List[Dict]) -> str:
        """从标题中清理链接文本"""
        cleaned_title = title

        for link in links:
            cleaned_title = cleaned_title.replace(link['url'], '').strip()
            prefixes = ['https://', 'http://', 'pan.baidu.com', 'pan.quark.cn']
            for prefix in prefixes:
                cleaned_title = cleaned_title.replace(prefix, '').strip()

        cleaned_title = re.sub(r'\s+', ' ', cleaned_title).strip()
        return cleaned_title

    def _is_new_content_title(self, text: str, current_keyword: str) -> bool:
        """判断是否是新的内容标题"""
        if not text:
            return False

        formatted_text = text.replace("-", "")

        if current_keyword.lower() in formatted_text.lower():
            return False

        link_indicators = ['百度', '夸克', 'https://', 'pan.baidu.com', 'pan.quark.cn', 'pwd=']
        if any(indicator in text for indicator in link_indicators):
            return False

        text_clean = text.strip()
        content_indicators = ['🔝', '【', '】', '更', '第', '全']
        has_content_features = any(indicator in text for indicator in content_indicators)

        if has_content_features:
            return True

        if 2 <= len(text_clean) <= 20:
            exclude_patterns = ['提取码', '密码', '链接', '下载', '资源', '时间']
            if not any(pattern in text_clean for pattern in exclude_patterns):
                return True

        return False
    
    def search(self, keyword: str) -> List[ContentInfo]:
        """搜索指定关键词的内容链接 - 多线程优化版本"""
        keyword = self._format_keyword(keyword)
        logger.info(f"开始搜索: {keyword}")
        
        results = []
        
        # 使用多线程并行获取文档数据
        def search_single_document(url):
            """搜索单个文档"""
            try:
                document_data = self._get_document_data(url)
                if not document_data:
                    return []
                
                doc_results = []
                for logic_block in document_data.get('content', []):
                    if logic_block.get('type') == 'logic_block':
                        block_results = self._find_content_links_precise(keyword, logic_block, url)
                        doc_results.extend(block_results)
                
                return doc_results
            except Exception as e:
                logger.error(f"搜索文档失败: {url} - {e}")
                return []
        
        # 并行搜索所有文档
        max_workers = min(len(self.document_urls), 4)
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {
                executor.submit(search_single_document, url): url
                for url in self.document_urls
            }
            
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    doc_results = future.result(timeout=15)
                    results.extend(doc_results)
                except Exception as e:
                    logger.error(f"处理文档搜索结果失败: {url} - {e}")
        
        logger.info(f"搜索完成: {keyword}, 找到 {len(results)} 个结果")
        return results

# 便捷函数
def search_content(keyword: str) -> List[ContentInfo]:
    """搜索内容的便捷函数"""
    searcher = WPSContentSearcher()
    return searcher.search(keyword)