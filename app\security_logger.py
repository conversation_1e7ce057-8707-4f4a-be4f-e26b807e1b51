"""
安全日志记录器
"""

import logging
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
from security_config import security_config

class SecurityLogger:
    """安全事件日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger("security")
        self.logger.setLevel(getattr(logging, security_config.LOG_LEVEL))
        
        # 创建安全日志处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          severity: str = "INFO", client_info: Optional[Dict[str, str]] = None):
        """
        记录安全事件
        
        Args:
            event_type: 事件类型
            details: 事件详情
            severity: 严重程度 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            client_info: 客户端信息
        """
        if not security_config.LOG_SECURITY_EVENTS:
            return
        
        event_data = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "severity": severity,
            "details": details
        }
        
        if client_info:
            event_data["client"] = client_info
        
        # 根据严重程度选择日志级别
        log_method = getattr(self.logger, severity.lower(), self.logger.info)
        log_method(json.dumps(event_data, ensure_ascii=False))
    
    def log_attack_attempt(self, attack_type: str, client_ip: str, user_agent: str, 
                          request_data: Dict[str, Any], blocked: bool = True):
        """记录攻击尝试"""
        self.log_security_event(
            event_type="attack_attempt",
            details={
                "attack_type": attack_type,
                "request_data": request_data,
                "blocked": blocked,
                "timestamp": time.time()
            },
            severity="WARNING",
            client_info={
                "ip": client_ip,
                "user_agent": user_agent
            }
        )
    
    def log_rate_limit_violation(self, client_id: str, endpoint: str, 
                                violation_count: int, blocked: bool = False):
        """记录频率限制违规"""
        self.log_security_event(
            event_type="rate_limit_violation",
            details={
                "endpoint": endpoint,
                "violation_count": violation_count,
                "blocked": blocked,
                "timestamp": time.time()
            },
            severity="WARNING" if not blocked else "ERROR",
            client_info={"client_id": client_id}
        )
    
    def log_input_validation_failure(self, input_type: str, input_value: str, 
                                   validation_error: str, client_ip: str):
        """记录输入验证失败"""
        self.log_security_event(
            event_type="input_validation_failure",
            details={
                "input_type": input_type,
                "input_value": input_value[:100],  # 只记录前100个字符
                "validation_error": validation_error,
                "timestamp": time.time()
            },
            severity="WARNING",
            client_info={"ip": client_ip}
        )
    
    def log_suspicious_activity(self, activity_type: str, client_id: str, 
                              activity_details: Dict[str, Any]):
        """记录可疑活动"""
        self.log_security_event(
            event_type="suspicious_activity",
            details={
                "activity_type": activity_type,
                "activity_details": activity_details,
                "timestamp": time.time()
            },
            severity="WARNING",
            client_info={"client_id": client_id}
        )
    
    def log_security_config_change(self, config_item: str, old_value: Any, 
                                  new_value: Any, changed_by: str):
        """记录安全配置变更"""
        self.log_security_event(
            event_type="security_config_change",
            details={
                "config_item": config_item,
                "old_value": str(old_value),
                "new_value": str(new_value),
                "changed_by": changed_by,
                "timestamp": time.time()
            },
            severity="INFO"
        )
    
    def log_authentication_event(self, event_type: str, user_id: str, 
                                client_ip: str, success: bool):
        """记录认证事件（如果有认证功能）"""
        self.log_security_event(
            event_type="authentication",
            details={
                "auth_event_type": event_type,
                "user_id": user_id,
                "success": success,
                "timestamp": time.time()
            },
            severity="INFO" if success else "WARNING",
            client_info={"ip": client_ip}
        )
    
    def log_performance_issue(self, issue_type: str, details: Dict[str, Any], 
                            severity: str = "WARNING"):
        """记录性能问题"""
        self.log_security_event(
            event_type="performance_issue",
            details={
                "issue_type": issue_type,
                "performance_details": details,
                "timestamp": time.time()
            },
            severity=severity
        )
    
    def log_system_event(self, event_type: str, details: Dict[str, Any]):
        """记录系统事件"""
        self.log_security_event(
            event_type="system_event",
            details={
                "system_event_type": event_type,
                "system_details": details,
                "timestamp": time.time()
            },
            severity="INFO"
        )

# 创建全局安全日志记录器实例
security_logger = SecurityLogger()