<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控仪表板 - 移动端电影搜索应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e1e8ed;
        }

        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e1e8ed;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-indicator {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-healthy {
            background: #d4edda;
            color: #155724;
        }

        .status-degraded {
            background: #fff3cd;
            color: #856404;
        }

        .status-unhealthy {
            background: #f8d7da;
            color: #721c24;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #666;
            font-size: 0.9rem;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .metric-value.warning {
            color: #f39c12;
        }

        .metric-value.error {
            color: #e74c3c;
        }

        .refresh-info {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-top: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 2rem;
            color: #e74c3c;
            background: #f8d7da;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            color: #856404;
        }

        .alert.critical {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e8ed;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: #f39c12;
        }

        .progress-fill.error {
            background: #e74c3c;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 监控仪表板</h1>
    </div>

    <div class="container">
        <div id="loading" class="loading">
            <p>正在加载监控数据...</p>
        </div>

        <div id="error" class="error" style="display: none;">
            <p>加载监控数据失败，请稍后重试</p>
        </div>

        <div id="dashboard" style="display: none;">
            <!-- 概览卡片 -->
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">系统状态</div>
                        <div id="health-status" class="status-indicator">检查中</div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">运行时间</span>
                        <span id="uptime" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">总请求数</span>
                        <span id="total-requests" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">总错误数</span>
                        <span id="total-errors" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">总攻击数</span>
                        <span id="total-attacks" class="metric-value">-</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">性能指标</div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">错误率</span>
                        <span id="error-rate" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">平均响应时间</span>
                        <span id="response-time" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">每分钟请求数</span>
                        <span id="requests-per-minute" class="metric-value">-</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">系统资源</div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">CPU 使用率</span>
                        <span id="cpu-usage" class="metric-value">-</span>
                    </div>
                    <div class="progress-bar">
                        <div id="cpu-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">内存使用率</span>
                        <span id="memory-usage" class="metric-value">-</span>
                    </div>
                    <div class="progress-bar">
                        <div id="memory-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">活跃连接数</span>
                        <span id="active-connections" class="metric-value">-</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">安全状态</div>
                    </div>
                    <div class="metric">
                        <span class="metric-label">最近攻击数</span>
                        <span id="recent-attacks" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">被阻止IP数</span>
                        <span id="blocked-ips" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">攻击频率</span>
                        <span id="attacks-per-hour" class="metric-value">-</span>
                    </div>
                </div>
            </div>

            <!-- 告警信息 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">告警信息</div>
                </div>
                <div id="alerts-container">
                    <p style="color: #666; text-align: center;">暂无告警</p>
                </div>
            </div>
        </div>

        <div class="refresh-info">
            <p>数据每30秒自动刷新 | 最后更新: <span id="last-update">-</span></p>
        </div>
    </div>

    <script>
        class MonitoringDashboard {
            constructor() {
                this.refreshInterval = 30000; // 30秒
                this.intervalId = null;
                this.init();
            }

            init() {
                this.loadDashboardData();
                this.startAutoRefresh();
            }

            async loadDashboardData() {
                try {
                    const response = await fetch('/api/monitoring/dashboard');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const data = await response.json();
                    if (data.success) {
                        this.updateDashboard(data.data);
                        this.hideError();
                        this.showDashboard();
                    } else {
                        throw new Error(data.message || '获取数据失败');
                    }
                } catch (error) {
                    console.error('加载监控数据失败:', error);
                    this.showError();
                }
            }

            updateDashboard(data) {
                // 更新概览信息
                this.updateElement('health-status', data.overview.status, 'text');
                this.updateStatusIndicator(data.overview.status);
                this.updateElement('uptime', data.overview.uptime);
                this.updateElement('total-requests', data.overview.total_requests.toLocaleString());
                this.updateElement('total-errors', data.overview.total_errors.toLocaleString());
                this.updateElement('total-attacks', data.overview.total_attacks.toLocaleString());

                // 更新性能指标
                this.updateElement('error-rate', `${data.performance.current_error_rate}%`);
                this.updateElement('response-time', `${data.performance.current_response_time}s`);
                this.updateElement('requests-per-minute', data.performance.requests_per_minute.toFixed(1));

                // 更新系统资源
                this.updateElement('cpu-usage', `${data.system.cpu_percent.toFixed(1)}%`);
                this.updateProgressBar('cpu-progress', data.system.cpu_percent);
                
                this.updateElement('memory-usage', `${data.system.memory_percent.toFixed(1)}%`);
                this.updateProgressBar('memory-progress', data.system.memory_percent);
                
                this.updateElement('active-connections', data.system.active_connections);

                // 更新安全状态
                this.updateElement('recent-attacks', data.security.recent_attacks);
                this.updateElement('blocked-ips', data.security.blocked_ips);
                this.updateElement('attacks-per-hour', data.security.attacks_per_hour.toFixed(1));

                // 更新告警信息
                this.updateAlerts(data.recent_issues);

                // 更新最后更新时间
                this.updateElement('last-update', new Date().toLocaleString());
            }

            updateElement(id, value, type = 'text') {
                const element = document.getElementById(id);
                if (element) {
                    if (type === 'html') {
                        element.innerHTML = value;
                    } else {
                        element.textContent = value;
                    }
                }
            }

            updateStatusIndicator(status) {
                const element = document.getElementById('health-status');
                if (element) {
                    element.className = `status-indicator status-${status}`;
                    
                    const statusText = {
                        'healthy': '健康',
                        'degraded': '降级',
                        'unhealthy': '异常'
                    };
                    
                    element.textContent = statusText[status] || status;
                }
            }

            updateProgressBar(id, percentage) {
                const element = document.getElementById(id);
                if (element) {
                    element.style.width = `${Math.min(percentage, 100)}%`;
                    
                    // 根据百分比设置颜色
                    element.className = 'progress-fill';
                    if (percentage > 80) {
                        element.classList.add('error');
                    } else if (percentage > 60) {
                        element.classList.add('warning');
                    }
                }
            }

            updateAlerts(issues) {
                const container = document.getElementById('alerts-container');
                if (!container) return;

                if (!issues || issues.length === 0) {
                    container.innerHTML = '<p style="color: #666; text-align: center;">暂无告警</p>';
                    return;
                }

                const alertsHtml = issues.map(issue => 
                    `<div class="alert">${issue}</div>`
                ).join('');

                container.innerHTML = alertsHtml;
            }

            showDashboard() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                document.getElementById('dashboard').style.display = 'block';
            }

            showError() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard').style.display = 'none';
                document.getElementById('error').style.display = 'block';
            }

            hideError() {
                document.getElementById('error').style.display = 'none';
            }

            startAutoRefresh() {
                this.intervalId = setInterval(() => {
                    this.loadDashboardData();
                }, this.refreshInterval);
            }

            stopAutoRefresh() {
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                    this.intervalId = null;
                }
            }
        }

        // 页面加载完成后初始化仪表板
        document.addEventListener('DOMContentLoaded', () => {
            window.dashboard = new MonitoringDashboard();
        });

        // 页面可见性变化时控制刷新
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                window.dashboard?.stopAutoRefresh();
            } else {
                window.dashboard?.startAutoRefresh();
            }
        });
    </script>
</body>
</html>