#!/usr/bin/env python3
"""
AES解密示例 - 演示如何解密链接
"""

import base64
import hashlib
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

def decrypt_link_example():
    """解密链接示例"""
    
    # 你提供的加密链接
    encrypted_link = "aes:VvI4dWPMPy+D0umION1qgXttWYBq1tdkF6jkegt2heQHWK/N5P2TX1Z8rWS+3RyV7nOZNKyDVbDXZuKnvUZCb+**************************"
    
    print("🔓 AES解密示例")
    print("=" * 50)
    print(f"加密链接: {encrypted_link}")
    
    try:
        # 步骤1：移除前缀
        base64_data = encrypted_link[4:]  # 移除 "aes:"
        
        # 步骤2：Base64解码
        encrypted_bytes = base64.b64decode(base64_data)
        
        # 步骤3：分离nonce和密文
        nonce = encrypted_bytes[:12]
        ciphertext = encrypted_bytes[12:]
        
        print(f"\n📊 解密过程:")
        print(f"1. Nonce: {nonce.hex()}")
        print(f"2. 密文长度: {len(ciphertext)} 字节")
        
        # 步骤4：重建密钥（与加密时相同）
        key_material = "your-encryption-key-32-chars-long"
        encryption_key = hashlib.sha256(key_material.encode('utf-8')).digest()[:32]
        
        # 步骤5：AES-GCM解密
        aesgcm = AESGCM(encryption_key)
        plaintext = aesgcm.decrypt(nonce, ciphertext, None)
        
        # 步骤6：转换为字符串
        original_link = plaintext.decode('utf-8')
        
        print(f"\n✅ 解密成功!")
        print(f"原始链接: {original_link}")
        
        # 验证链接类型
        if "pan.baidu.com" in original_link:
            print("🔗 链接类型: 百度网盘")
        elif "pan.quark.cn" in original_link:
            print("🔗 链接类型: 夸克网盘")
        else:
            print("🔗 链接类型: 其他")
            
        return original_link
        
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return None

def demonstrate_encryption_randomness():
    """演示加密的随机性"""
    print(f"\n🎲 加密随机性演示")
    print("=" * 50)
    
    # 模拟加密相同链接多次
    test_link = "https://pan.baidu.com/s/1234567890"
    
    print(f"原始链接: {test_link}")
    print(f"多次加密同一链接的结果:")
    
    # 这里只是演示概念，实际需要调用加密函数
    import os
    for i in range(3):
        # 模拟随机nonce
        fake_nonce = os.urandom(12).hex()
        print(f"  第{i+1}次: nonce={fake_nonce[:16]}... (每次都不同)")
    
    print(f"\n💡 关键点:")
    print(f"✅ 相同链接每次加密结果都不同 (因为nonce随机)")
    print(f"✅ 这防止了模式分析攻击")
    print(f"✅ 爬虫无法通过密文推断明文")

if __name__ == "__main__":
    # 解密示例
    decrypted = decrypt_link_example()
    
    # 随机性演示
    demonstrate_encryption_randomness()
    
    if decrypted:
        print(f"\n🎯 总结:")
        print(f"✅ AES-256-GCM提供了军用级的安全性")
        print(f"✅ 即使爬虫获得密文，没有密钥也无法解密")
        print(f"⚠️ 但如果爬虫获得了服务器代码，就能提取密钥")
        print(f"💡 建议: 定期更换加密密钥，使用环境变量管理")
