"""
安全模块 - 输入验证和安全防护
"""

import re
import html
import logging
import hashlib
import time
from typing import Tuple, List, Dict, Any
from urllib.parse import urlparse
import ipaddress

logger = logging.getLogger(__name__)

# 延迟导入以避免循环依赖
def get_security_logger():
    from security_logger import security_logger
    return security_logger

class SecurityValidator:
    """安全验证器"""
    
    # 危险模式列表
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # script标签
        r'javascript:',               # javascript协议
        r'on\w+\s*=',                # 事件处理器
        r'<iframe[^>]*>',            # iframe标签
        r'<object[^>]*>',            # object标签
        r'<embed[^>]*>',             # embed标签
        r'<link[^>]*>',              # link标签
        r'<meta[^>]*>',              # meta标签
        r'<style[^>]*>.*?</style>',  # style标签
        r'expression\s*\(',          # CSS表达式
        r'url\s*\(',                 # CSS url函数
        r'@import',                  # CSS import
        r'vbscript:',                # vbscript协议
        r'data:text/html',           # data协议HTML
        r'data:application/',        # data协议应用
        r'<\?php',                   # PHP标签
        r'<%',                       # ASP标签
        r'\$\{',                     # 模板注入
        r'{{',                       # 模板注入
        r'eval\s*\(',               # eval函数
        r'exec\s*\(',               # exec函数
        r'system\s*\(',             # system函数
        r'shell_exec\s*\(',         # shell_exec函数
        r'passthru\s*\(',           # passthru函数
        r'file_get_contents\s*\(',  # file_get_contents函数
        r'fopen\s*\(',              # fopen函数
        r'include\s*\(',            # include函数
        r'require\s*\(',            # require函数
    ]
    
    # SQL注入模式
    SQL_INJECTION_PATTERNS = [
        r'union\s+select',
        r'drop\s+table',
        r'delete\s+from',
        r'insert\s+into',
        r'update\s+set',
        r'alter\s+table',
        r'create\s+table',
        r'exec\s*\(',
        r'sp_\w+',
        r'xp_\w+',
        r'--\s*$',
        r'/\*.*?\*/',
        r';\s*--',
        r';\s*/\*',
    ]
    
    # 命令注入模式
    COMMAND_INJECTION_PATTERNS = [
        r';\s*ls\s',
        r';\s*cat\s',
        r';\s*pwd\s',
        r';\s*whoami\s',
        r';\s*id\s',
        r';\s*ps\s',
        r';\s*netstat\s',
        r';\s*ifconfig\s',
        r';\s*ping\s',
        r';\s*wget\s',
        r';\s*curl\s',
        r'\|\s*nc\s',
        r'\|\s*telnet\s',
        r'&&\s*\w+',
        r'\|\|\s*\w+',
        r'`[^`]*`',
        r'\$\([^)]*\)',
    ]
    
    # 路径遍历模式
    PATH_TRAVERSAL_PATTERNS = [
        r'\.\./+',
        r'\.\.\\+',
        r'/etc/passwd',
        r'/etc/shadow',
        r'\\windows\\system32',
        r'\\boot\.ini',
        r'file://',
        r'ftp://',
    ]
    
    @staticmethod
    def validate_search_keyword(keyword: str) -> Tuple[bool, str]:
        """
        验证搜索关键词
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not keyword:
            return False, "搜索关键词不能为空"
        
        # 长度检查
        if len(keyword) > 50:
            return False, "搜索关键词不能超过50个字符"
        
        if len(keyword) < 2:
            return False, "搜索关键词至少需要2个字符"
        
        # 检查是否只包含空白字符
        if not keyword.strip():
            return False, "搜索关键词不能只包含空白字符"
        
        # 检查危险模式
        keyword_lower = keyword.lower()
        for pattern in SecurityValidator.DANGEROUS_PATTERNS:
            if re.search(pattern, keyword_lower, re.IGNORECASE):
                logger.warning(f"检测到XSS攻击尝试: {keyword}")
                # 记录安全事件
                try:
                    security_logger = get_security_logger()
                    security_logger.log_attack_attempt(
                        attack_type="xss_attempt",
                        client_ip="unknown",
                        user_agent="unknown",
                        request_data={"keyword": keyword[:100]},
                        blocked=True
                    )
                except:
                    pass
                return False, "搜索关键词包含不安全内容"
        
        # 检查SQL注入
        for pattern in SecurityValidator.SQL_INJECTION_PATTERNS:
            if re.search(pattern, keyword_lower, re.IGNORECASE):
                logger.warning(f"检测到SQL注入尝试: {keyword}")
                return False, "搜索关键词包含不安全内容"
        
        # 检查命令注入
        for pattern in SecurityValidator.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, keyword_lower, re.IGNORECASE):
                logger.warning(f"检测到命令注入尝试: {keyword}")
                return False, "搜索关键词包含不安全内容"
        
        # 检查路径遍历
        for pattern in SecurityValidator.PATH_TRAVERSAL_PATTERNS:
            if re.search(pattern, keyword_lower, re.IGNORECASE):
                logger.warning(f"检测到路径遍历尝试: {keyword}")
                return False, "搜索关键词包含不安全内容"
        
        # 检查特殊字符（只检查真正危险的字符）
        invalid_chars = re.compile(r'[<>"&;|`${}[\]\\]')
        if invalid_chars.search(keyword):
            return False, "搜索关键词包含非法字符"
        
        # 检查字符类型（允许中文、英文、数字、空格和常见标点符号）
        # 包含：中文字符、英文字母、数字、空格、常见中英文标点符号、间隔号等
        allowed_pattern = re.compile(r'^[\u4e00-\u9fff\w\s\.\-\!\?\，\。\！\？\：\；\（\）\【\】\《\》\·\•\～\~\&\+\*\#\@\%\^\=\|\'\"\`]+$')
        if not allowed_pattern.match(keyword):
            return False, "搜索关键词包含不支持的字符类型"
        
        # 检查重复字符（防止DoS攻击）
        if SecurityValidator._has_excessive_repetition(keyword):
            return False, "搜索关键词包含过多重复字符"
        
        return True, ""
    
    @staticmethod
    def _has_excessive_repetition(text: str, max_repeat: int = 10) -> bool:
        """检查是否有过多重复字符"""
        for char in set(text):
            if text.count(char) > max_repeat:
                return True
        return False
    
    @staticmethod
    def sanitize_input(input_text: str) -> str:
        """
        清理和转义输入内容
        
        Args:
            input_text: 输入文本
            
        Returns:
            str: 清理后的文本
        """
        if not isinstance(input_text, str):
            return str(input_text)
        
        # HTML转义
        sanitized = html.escape(input_text, quote=True)
        
        # 移除潜在的危险字符
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', sanitized)
        
        return sanitized
    
    @staticmethod
    def validate_url(url: str) -> Tuple[bool, str]:
        """
        验证URL安全性
        
        Args:
            url: 要验证的URL
            
        Returns:
            Tuple[bool, str]: (是否安全, 错误信息)
        """
        if not url:
            return False, "URL不能为空"
        
        try:
            parsed = urlparse(url)
            
            # 检查协议
            if parsed.scheme not in ['http', 'https']:
                return False, "不支持的URL协议"
            
            # 检查域名
            if not parsed.netloc:
                return False, "无效的URL格式"
            
            # 检查是否为内网地址
            try:
                # 提取主机名
                hostname = parsed.hostname
                if hostname:
                    # 检查是否为IP地址
                    try:
                        ip = ipaddress.ip_address(hostname)
                        if ip.is_private or ip.is_loopback or ip.is_link_local:
                            return False, "不允许访问内网地址"
                    except ValueError:
                        # 不是IP地址，检查是否为本地域名
                        if hostname.lower() in ['localhost', '127.0.0.1', '0.0.0.0']:
                            return False, "不允许访问本地地址"
            except Exception:
                pass
            
            # 检查URL长度
            if len(url) > 2048:
                return False, "URL长度超出限制"
            
            return True, ""
            
        except Exception as e:
            logger.warning(f"URL验证异常: {url} - {e}")
            return False, "URL格式无效"

class RateLimiter:
    """增强的频率限制器"""

    def __init__(self):
        self.client_requests = {}
        self.client_violations = {}
        self.blocked_ips = {}
        self.suspicious_patterns = {}

        # 从配置文件导入配置，避免硬编码
        from security_config import security_config
        self.max_violations = security_config.MAX_VIOLATIONS
        self.block_duration = security_config.BLOCK_DURATION
        self.cleanup_interval = 300  # 5分钟清理一次
        self.last_cleanup = time.time()
    
    def check_rate_limit(self, client_id: str, endpoint: str, request_data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        检查频率限制
        
        Args:
            client_id: 客户端标识
            endpoint: 接口名称
            request_data: 请求数据（用于模式检测）
            
        Returns:
            Tuple[bool, str]: (是否允许, 错误信息)
        """
        now = time.time()
        
        # 定期清理
        if now - self.last_cleanup > self.cleanup_interval:
            self._cleanup_expired_data()
            self.last_cleanup = now
        
        # 检查是否被阻止
        if client_id in self.blocked_ips:
            if now < self.blocked_ips[client_id]:
                remaining = int(self.blocked_ips[client_id] - now)
                return False, f"客户端已被临时阻止，剩余时间: {remaining}秒"
            else:
                # 解除阻止
                del self.blocked_ips[client_id]
                self.client_violations[client_id] = 0
        
        # 检查可疑模式
        if request_data and self._detect_suspicious_pattern(client_id, request_data):
            self._record_violation(client_id, "suspicious_pattern")
            return False, "检测到可疑请求模式"
        
        # 基本频率限制检查
        endpoint_key = f"{client_id}:{endpoint}"
        
        # 获取配置
        limits = self._get_endpoint_limits(endpoint)
        
        # 清理过期请求
        if endpoint_key in self.client_requests:
            self.client_requests[endpoint_key] = [
                req_time for req_time in self.client_requests[endpoint_key]
                if now - req_time < limits['window']
            ]
        else:
            self.client_requests[endpoint_key] = []
        
        # 检查突发请求
        recent_requests = [
            req_time for req_time in self.client_requests[endpoint_key]
            if now - req_time < limits['burst_window']
        ]
        
        if len(recent_requests) >= limits['burst']:
            self._record_violation(client_id, "burst_limit")
            return False, f"请求过于频繁，请等待 {limits['burst_window']} 秒后重试"
        
        # 检查常规限制
        if len(self.client_requests[endpoint_key]) >= limits['requests']:
            self._record_violation(client_id, "rate_limit")
            return False, f"该接口每分钟最多允许 {limits['requests']} 次请求"
        
        # 记录请求
        self.client_requests[endpoint_key].append(now)
        return True, ""
    
    def _get_endpoint_limits(self, endpoint: str) -> Dict[str, int]:
        """获取接口限制配置"""
        from security_config import security_config
        return security_config.get_rate_limit_config(endpoint)
    
    def _detect_suspicious_pattern(self, client_id: str, request_data: Dict[str, Any]) -> bool:
        """检测可疑请求模式"""
        now = time.time()
        
        if client_id not in self.suspicious_patterns:
            self.suspicious_patterns[client_id] = {
                'requests': [],
                'keywords': [],
                'last_check': now
            }
        
        patterns = self.suspicious_patterns[client_id]
        
        # 检查关键词模式
        if 'keyword' in request_data:
            keyword = request_data['keyword']
            patterns['keywords'].append((keyword, now))
            
            # 保留最近1小时的关键词
            patterns['keywords'] = [
                (kw, timestamp) for kw, timestamp in patterns['keywords']
                if now - timestamp < 3600
            ]
            
            # 检查重复关键词
            recent_keywords = [kw for kw, timestamp in patterns['keywords'] if now - timestamp < 300]
            if len(recent_keywords) > 20:  # 5分钟内超过20个关键词
                return True
            
            # 检查相似关键词（可能是自动化攻击）
            if len(set(recent_keywords)) < len(recent_keywords) * 0.3:  # 重复率超过70%
                return True
        
        return False
    
    def _record_violation(self, client_id: str, violation_type: str):
        """记录违规行为"""
        now = time.time()
        
        if client_id not in self.client_violations:
            self.client_violations[client_id] = 0
        
        self.client_violations[client_id] += 1
        
        logger.warning(f"客户端 {client_id} 违规: {violation_type}, 累计违规: {self.client_violations[client_id]}")
        
        # 达到最大违规次数，临时阻止
        if self.client_violations[client_id] >= self.max_violations:
            self.blocked_ips[client_id] = now + self.block_duration
            logger.error(f"客户端 {client_id} 因频繁违规被临时阻止 {self.block_duration} 秒")
    
    def _cleanup_expired_data(self):
        """清理过期数据"""
        now = time.time()
        
        # 清理过期的请求记录
        expired_keys = []
        for endpoint_key, requests in self.client_requests.items():
            # 保留最近1小时的数据
            self.client_requests[endpoint_key] = [
                req_time for req_time in requests
                if now - req_time < 3600
            ]
            if not self.client_requests[endpoint_key]:
                expired_keys.append(endpoint_key)
        
        for key in expired_keys:
            del self.client_requests[key]
        
        # 清理过期的违规记录
        expired_violations = []
        for client_id, violation_count in self.client_violations.items():
            # 如果客户端没有活跃请求，清理违规记录
            has_active_requests = any(
                endpoint_key.startswith(f"{client_id}:")
                for endpoint_key in self.client_requests.keys()
            )
            if not has_active_requests:
                expired_violations.append(client_id)
        
        for client_id in expired_violations:
            del self.client_violations[client_id]
        
        # 清理过期的阻止记录
        expired_blocks = [
            client_id for client_id, unblock_time in self.blocked_ips.items()
            if now >= unblock_time
        ]
        for client_id in expired_blocks:
            del self.blocked_ips[client_id]
        
        # 清理可疑模式记录
        for client_id in list(self.suspicious_patterns.keys()):
            if now - self.suspicious_patterns[client_id]['last_check'] > 3600:
                del self.suspicious_patterns[client_id]
        
        logger.info(f"清理安全数据: 删除 {len(expired_keys)} 个请求记录, {len(expired_violations)} 个违规记录, {len(expired_blocks)} 个阻止记录")

# 全局实例
security_validator = SecurityValidator()
rate_limiter = RateLimiter()