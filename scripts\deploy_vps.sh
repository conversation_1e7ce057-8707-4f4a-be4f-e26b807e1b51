#!/bin/bash

# 移动端电影搜索应用 - VPS部署脚本
# 使用方法: ./scripts/deploy_vps.sh [production|staging]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    log_error "环境参数错误。使用方法: ./scripts/deploy_vps.sh [production|staging]"
    exit 1
fi

log_info "开始VPS部署到 $ENVIRONMENT 环境..."

# 配置变量
APP_NAME="movie-search"
APP_USER="movieapp"
APP_DIR="/opt/movie-search"
SERVICE_NAME="movie-search"

# 环境特定配置
if [[ "$ENVIRONMENT" == "production" ]]; then
    PORT=8506
    WORKERS=2
    LOG_LEVEL="WARNING"
    DOMAIN="yourdomain.com"
else
    PORT=8507
    WORKERS=1
    LOG_LEVEL="INFO"
    DOMAIN="staging.yourdomain.com"
fi

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "不支持的操作系统"
        exit 1
    fi
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$PYTHON_VERSION < 3.8" | bc -l) -eq 1 ]]; then
        log_error "Python版本过低，需要3.8+，当前版本: $PYTHON_VERSION"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装"
        exit 1
    fi
    
    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        log_error "systemd 未安装，无法创建系统服务"
        exit 1
    fi
    
    log_success "系统要求检查完成"
}

# 创建应用用户
create_app_user() {
    log_info "创建应用用户..."
    
    if ! id "$APP_USER" &>/dev/null; then
        sudo useradd -r -s /bin/false -d "$APP_DIR" "$APP_USER"
        log_success "用户 $APP_USER 创建成功"
    else
        log_info "用户 $APP_USER 已存在"
    fi
}

# 创建应用目录
create_app_directory() {
    log_info "创建应用目录..."
    
    sudo mkdir -p "$APP_DIR"/{app,static,logs,config,backups}
    sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    sudo chmod -R 755 "$APP_DIR"
    
    log_success "应用目录创建完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包管理器
    sudo apt-get update
    
    # 安装必要的系统包
    sudo apt-get install -y \
        python3-venv \
        python3-pip \
        nginx \
        supervisor \
        curl \
        wget \
        git \
        htop \
        ufw \
        certbot \
        python3-certbot-nginx
    
    log_success "系统依赖安装完成"
}

# 复制应用文件
copy_application_files() {
    log_info "复制应用文件..."
    
    # 复制应用代码
    sudo cp -r app/* "$APP_DIR/app/"
    sudo cp -r static/* "$APP_DIR/static/"
    sudo cp requirements.txt "$APP_DIR/"
    sudo cp .env.example "$APP_DIR/.env"
    
    # 设置文件权限
    sudo chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    sudo chmod -R 644 "$APP_DIR"/{app,static}
    sudo chmod -R 755 "$APP_DIR"/logs
    
    log_success "应用文件复制完成"
}

# 创建Python虚拟环境
create_virtual_environment() {
    log_info "创建Python虚拟环境..."
    
    sudo -u "$APP_USER" python3 -m venv "$APP_DIR/venv"
    sudo -u "$APP_USER" "$APP_DIR/venv/bin/pip" install --upgrade pip
    sudo -u "$APP_USER" "$APP_DIR/venv/bin/pip" install -r "$APP_DIR/requirements.txt"
    
    log_success "虚拟环境创建完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    # 更新.env文件
    sudo -u "$APP_USER" tee "$APP_DIR/.env" > /dev/null <<EOF
ENVIRONMENT=$ENVIRONMENT
HOST=127.0.0.1
PORT=$PORT
WORKERS=$WORKERS
LOG_LEVEL=$LOG_LEVEL
LOG_DIR=$APP_DIR/logs
RELOAD=false

# 安全配置
ALLOWED_ORIGINS=https://$DOMAIN,https://www.$DOMAIN
API_KEYS=prod_$(openssl rand -hex 16):Production User
ENABLE_API_KEY_AUTH=true

# 加密配置
LINK_ENCRYPTION_KEY=$(openssl rand -hex 16)
SIGNATURE_SECRET=$(openssl rand -hex 32)

# 其他配置保持默认值
SEARCH_RATE_LIMIT=3
HOT_MOVIES_RATE_LIMIT=10
DEFAULT_RATE_LIMIT=20
MAX_VIOLATIONS=5
BLOCK_DURATION=120
EOF
    
    log_success "环境变量配置完成"
}

# 主函数
main() {
    log_info "开始VPS部署流程..."
    
    check_requirements
    create_app_user
    create_app_directory
    install_system_dependencies
    copy_application_files
    create_virtual_environment
    configure_environment
    
    log_success "VPS部署基础配置完成！"
    log_info "接下来需要："
    log_info "1. 运行 ./scripts/setup_service.sh 配置系统服务"
    log_info "2. 运行 ./scripts/setup_nginx.sh 配置Nginx"
    log_info "3. 配置SSL证书"
    log_info "4. 启动服务"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
