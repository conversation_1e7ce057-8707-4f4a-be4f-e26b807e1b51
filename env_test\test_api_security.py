#!/usr/bin/env python3
"""
API安全功能测试脚本
"""

import requests
import json
import time
import hmac
import hashlib
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 测试配置
API_BASE_URL = "http://localhost:8080"
API_KEY = "demo_key_123456"
API_SECRET = "your-secret-key-change-in-production"
INVALID_API_KEY = "invalid_key_123"

class APISecurityTester:
    """API安全测试器"""
    
    def __init__(self):
        self.base_url = API_BASE_URL
        self.api_key = API_KEY
        self.api_secret = API_SECRET
        self.session = requests.Session()
    
    def generate_signature(self, method, path, timestamp, body=""):
        """生成请求签名"""
        sign_string = f"{method}\n{path}\n{timestamp}\n{body}"
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def make_request(self, method, path, data=None, api_key=None, 
                    use_signature=True, custom_timestamp=None):
        """发送API请求"""
        timestamp = custom_timestamp or str(int(time.time()))
        body = json.dumps(data) if data else ""
        api_key = api_key or self.api_key
        
        headers = {
            "X-API-Key": api_key,
            "X-Timestamp": timestamp,
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        if use_signature:
            signature = self.generate_signature(method, path, timestamp, body)
            headers["X-Signature"] = signature
        
        url = f"{self.base_url}{path}"
        
        try:
            if method == "GET":
                response = self.session.get(url, headers=headers, timeout=10)
            else:
                response = self.session.post(url, headers=headers, data=body, timeout=10)
            
            return response
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
    
    def test_api_key_authentication(self):
        """测试API密钥认证"""
        print("\n=== 测试API密钥认证 ===")
        
        # 测试有效API密钥
        print("1. 测试有效API密钥...")
        response = self.make_request("GET", "/api/hot-movies")
        if response and response.status_code == 200:
            print("✅ 有效API密钥认证成功")
        else:
            print(f"❌ 有效API密钥认证失败: {response.status_code if response else 'No response'}")
        
        # 测试无效API密钥
        print("2. 测试无效API密钥...")
        response = self.make_request("GET", "/api/hot-movies", api_key=INVALID_API_KEY)
        if response and response.status_code == 401:
            print("✅ 无效API密钥正确被拒绝")
        else:
            print(f"❌ 无效API密钥处理异常: {response.status_code if response else 'No response'}")
        
        # 测试缺少API密钥
        print("3. 测试缺少API密钥...")
        response = self.make_request("GET", "/api/hot-movies", api_key="")
        if response and response.status_code == 401:
            print("✅ 缺少API密钥正确被拒绝")
        else:
            print(f"❌ 缺少API密钥处理异常: {response.status_code if response else 'No response'}")
    
    def test_request_signature(self):
        """测试请求签名验证"""
        print("\n=== 测试请求签名验证 ===")
        
        # 测试有效签名
        print("1. 测试有效签名...")
        response = self.make_request("POST", "/api/search", {"keyword": "测试"})
        if response and response.status_code == 200:
            print("✅ 有效签名验证成功")
        else:
            print(f"❌ 有效签名验证失败: {response.status_code if response else 'No response'}")
        
        # 测试无效签名
        print("2. 测试无效签名...")
        response = self.make_request("POST", "/api/search", {"keyword": "测试"}, use_signature=False)
        if response and response.status_code == 401:
            print("✅ 无效签名正确被拒绝")
        else:
            print(f"❌ 无效签名处理异常: {response.status_code if response else 'No response'}")
        
        # 测试过期时间戳
        print("3. 测试过期时间戳...")
        old_timestamp = str(int(time.time()) - 600)  # 10分钟前
        response = self.make_request("POST", "/api/search", {"keyword": "测试"}, 
                                   custom_timestamp=old_timestamp)
        if response and response.status_code == 401:
            print("✅ 过期时间戳正确被拒绝")
        else:
            print(f"❌ 过期时间戳处理异常: {response.status_code if response else 'No response'}")
    
    def test_rate_limiting(self):
        """测试频率限制"""
        print("\n=== 测试频率限制 ===")
        
        print("1. 测试搜索接口频率限制（1次/分钟）...")
        
        # 第一次请求应该成功
        response1 = self.make_request("POST", "/api/search", {"keyword": "测试1"})
        if response1 and response1.status_code == 200:
            print("✅ 第一次搜索请求成功")
        else:
            print(f"❌ 第一次搜索请求失败: {response1.status_code if response1 else 'No response'}")
        
        # 立即第二次请求应该被限制
        response2 = self.make_request("POST", "/api/search", {"keyword": "测试2"})
        if response2 and response2.status_code == 429:
            print("✅ 第二次搜索请求正确被限制")
        else:
            print(f"❌ 频率限制未生效: {response2.status_code if response2 else 'No response'}")
    
    def test_link_encryption(self):
        """测试链接加密"""
        print("\n=== 测试链接加密 ===")
        
        # 先进行搜索获取加密链接
        print("1. 获取搜索结果...")
        search_response = self.make_request("POST", "/api/search", {"keyword": "测试"})
        
        if not search_response or search_response.status_code != 200:
            print("❌ 无法获取搜索结果进行链接加密测试")
            return
        
        search_data = search_response.json()
        
        if not search_data.get("success") or not search_data.get("data"):
            print("❌ 搜索结果为空，无法测试链接加密")
            return
        
        # 检查链接是否被加密
        first_result = search_data["data"][0]
        if "links" in first_result and first_result["links"]:
            encrypted_link = first_result["links"][0]["url"]
            
            if encrypted_link.startswith("encrypted:"):
                print("✅ 链接已被加密")
                
                # 测试链接解密
                print("2. 测试链接解密...")
                decrypt_response = self.make_request("POST", "/api/decrypt-link", 
                                                   {"encrypted_link": encrypted_link})
                
                if decrypt_response and decrypt_response.status_code == 200:
                    decrypt_data = decrypt_response.json()
                    if decrypt_data.get("success"):
                        decrypted_link = decrypt_data.get("decrypted_link")
                        print(f"✅ 链接解密成功: {decrypted_link[:50]}...")
                    else:
                        print("❌ 链接解密失败")
                else:
                    print(f"❌ 链接解密请求失败: {decrypt_response.status_code if decrypt_response else 'No response'}")
            else:
                print("❌ 链接未被加密")
        else:
            print("❌ 搜索结果中没有链接")
    
    def test_log_sanitization(self):
        """测试日志脱敏（通过发送包含敏感信息的请求）"""
        print("\n=== 测试日志脱敏 ===")
        
        # 发送包含敏感信息的搜索请求
        sensitive_keyword = "password=123456 api_key=secret_key"
        response = self.make_request("POST", "/api/search", {"keyword": sensitive_keyword})
        
        if response:
            print("✅ 包含敏感信息的请求已处理（检查日志文件确认脱敏效果）")
        else:
            print("❌ 包含敏感信息的请求处理失败")
    
    def run_all_tests(self):
        """运行所有安全测试"""
        print("开始API安全功能测试...")
        print(f"测试目标: {self.base_url}")
        print(f"API密钥: {self.api_key}")
        
        try:
            self.test_api_key_authentication()
            self.test_request_signature()
            self.test_rate_limiting()
            self.test_link_encryption()
            self.test_log_sanitization()
            
            print("\n=== 测试完成 ===")
            print("请检查应用日志文件以确认安全功能正常工作")
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("API安全功能测试脚本")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        return
    
    # 运行测试
    tester = APISecurityTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
