<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>频率限制调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #notification { 
            position: fixed; 
            top: 20px; 
            right: 20px; 
            background: white; 
            border: 1px solid #ccc; 
            padding: 15px; 
            border-radius: 5px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        .notification.warning { border-left: 4px solid #ff9800; }
        .notification.error { border-left: 4px solid #f44336; }
        .notification.success { border-left: 4px solid #4caf50; }
    </style>
</head>
<body>
    <div class="container">
        <h1>频率限制调试页面</h1>
        
        <div>
            <button onclick="testSearch()">测试搜索请求</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>
        
        <div id="logs"></div>
        
        <div id="notification" class="notification">
            <div id="notification-message"></div>
        </div>
    </div>

    <script>
        let sessionToken = null;
        let fingerprint = null;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(logDiv);
            logs.scrollTop = logs.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notification-message');
            
            messageEl.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            log(`显示通知: ${message} (类型: ${type})`, type);
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        function generateBrowserFingerprint() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Browser fingerprint', 2, 2);
            
            const fingerprintData = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                new Date().getTimezoneOffset(),
                canvas.toDataURL(),
                navigator.hardwareConcurrency || 0
            ].join('|');
            
            let hash = 0;
            for (let i = 0; i < fingerprintData.length; i++) {
                const char = fingerprintData.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(16);
        }

        async function getSessionToken() {
            if (sessionToken) {
                log('使用已有的会话令牌');
                return sessionToken;
            }

            try {
                fingerprint = generateBrowserFingerprint();
                log(`生成浏览器指纹: ${fingerprint}`);

                const response = await fetch('/api/session', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        fingerprint: fingerprint,
                        timestamp: Date.now()
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    sessionToken = data.token;
                    log(`获取会话令牌成功: ${sessionToken.substring(0, 16)}...`, 'success');
                    return sessionToken;
                } else {
                    log(`获取会话令牌失败: ${response.status}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`获取会话令牌异常: ${error.message}`, 'error');
                return null;
            }
        }

        async function testSearch() {
            log('开始测试搜索请求...');
            
            try {
                // 获取会话令牌
                const token = await getSessionToken();
                if (!token) {
                    log('无法获取会话令牌，测试终止', 'error');
                    return;
                }

                // 发送搜索请求
                log('发送搜索请求...');
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Session-Token': token,
                        'X-Browser-Fingerprint': fingerprint,
                        'X-Timestamp': Date.now().toString()
                    },
                    body: JSON.stringify({ keyword: '测试电影' })
                });

                log(`搜索响应状态码: ${response.status}`);
                log(`搜索响应状态文本: ${response.statusText}`);

                // 专门处理429状态码
                if (response.status === 429) {
                    log('检测到429状态码，处理频率限制', 'warning');
                    
                    const data = await response.json();
                    log(`429响应数据: ${JSON.stringify(data)}`, 'warning');
                    
                    const errorMessage = data.error?.message || '请求过于频繁';
                    log(`频率限制错误信息: ${errorMessage}`, 'warning');
                    
                    // 提取剩余时间
                    const timeMatch = errorMessage.match(/(\d+)秒/);
                    const remainingTime = timeMatch ? timeMatch[1] : null;
                    log(`提取的剩余时间: ${remainingTime}秒`, 'warning');
                    
                    let userMessage = '搜索过于频繁，请稍后再试';
                    if (remainingTime) {
                        const minutes = Math.ceil(remainingTime / 60);
                        userMessage = `搜索过于频繁，请等待 ${minutes} 分钟后再试`;
                    }
                    
                    log(`准备显示用户消息: ${userMessage}`, 'warning');
                    showNotification(userMessage, 'warning');
                    return;
                }

                // 处理其他响应
                const data = await response.json();
                log(`搜索响应数据: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    log(`搜索成功，找到 ${data.data?.length || 0} 个结果`, 'success');
                    showNotification(`搜索成功，找到 ${data.data?.length || 0} 个结果`, 'success');
                } else {
                    log(`搜索失败: ${data.error?.message || '未知错误'}`, 'error');
                    showNotification(`搜索失败: ${data.error?.message || '未知错误'}`, 'error');
                }

            } catch (error) {
                log(`测试搜索异常: ${error.message}`, 'error');
                showNotification(`测试搜索异常: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('调试页面加载完成');
            log('点击"测试搜索请求"按钮开始测试');
        });
    </script>
</body>
</html>
