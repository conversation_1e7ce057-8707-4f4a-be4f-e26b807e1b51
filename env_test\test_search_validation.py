#!/usr/bin/env python3
"""
测试搜索关键词验证功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'app'))

from security import SecurityValidator

def test_search_validation():
    """测试搜索关键词验证"""
    
    print("🧪 测试搜索关键词验证功能")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        # 正常情况
        ("利剑·玫瑰", True, "包含间隔号的中文电影名"),
        ("复仇者联盟", True, "纯中文电影名"),
        ("Spider-Man", True, "英文电影名带连字符"),
        ("变形金刚3", True, "中文+数字"),
        ("阿凡达：水之道", True, "中文冒号"),
        ("速度与激情9", True, "中文与符号"),
        ("哈利·波特", True, "中文间隔号"),
        ("X战警：逆转未来", True, "英文+中文+冒号"),
        ("复仇者联盟4：终局之战", True, "复杂电影名"),
        ("蜘蛛侠：英雄归来", True, "中文冒号电影名"),
        ("钢铁侠 Iron Man", True, "中英文混合"),
        ("变形金刚5：最后的骑士", True, "数字+冒号"),
        ("星球大战：原力觉醒", True, "经典电影名"),
        ("加勒比海盗：黑珍珠号的诅咒", True, "长电影名"),
        ("指环王：护戒使者", True, "奇幻电影名"),
        ("哈利·波特与魔法石", True, "系列电影名"),
        ("变形金刚：超能勇士崛起", True, "动画电影名"),
        ("速度与激情：特别行动", True, "动作电影名"),
        ("复仇者联盟：无限战争", True, "超级英雄电影"),
        ("蜘蛛侠：平行宇宙", True, "动画超级英雄电影"),
        
        # 边界情况
        ("电影", True, "最短有效长度"),
        ("a", False, "太短"),
        ("", False, "空字符串"),
        ("   ", False, "只有空格"),
        ("a" * 51, False, "太长"),
        
        # 特殊字符测试
        ("电影@#%", True, "包含特殊符号"),
        ("电影&游戏", False, "包含危险字符&"),
        ("电影<script>", False, "包含HTML标签"),
        ("电影'OR'1'='1", False, "SQL注入尝试"),
        ("电影;ls", False, "命令注入尝试"),
        ("电影../etc", False, "路径遍历尝试"),
        
        # 重复字符测试
        ("aaaaaaaaaaaaa", False, "过多重复字符"),
        ("电影aaa", True, "少量重复字符"),
        
        # Unicode字符测试
        ("电影🎬", True, "包含emoji"),
        ("电影★", True, "包含星号"),
        ("电影♥", True, "包含心形"),
    ]
    
    passed = 0
    failed = 0
    
    for keyword, expected, description in test_cases:
        try:
            is_valid, error_msg = SecurityValidator.validate_search_keyword(keyword)
            
            if is_valid == expected:
                status = "✅ PASS"
                passed += 1
            else:
                status = "❌ FAIL"
                failed += 1
                
            print(f"{status} | {description}")
            print(f"     关键词: '{keyword}'")
            print(f"     期望: {'有效' if expected else '无效'}, 实际: {'有效' if is_valid else '无效'}")
            if not is_valid:
                print(f"     错误: {error_msg}")
            print()
            
        except Exception as e:
            print(f"❌ ERROR | {description}")
            print(f"     关键词: '{keyword}'")
            print(f"     异常: {e}")
            print()
            failed += 1
    
    print("=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    print(f"成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查验证逻辑")
        return False

def test_specific_movie_names():
    """测试具体的电影名称"""
    
    print("\n🎬 测试具体电影名称")
    print("=" * 50)
    
    movie_names = [
        "利剑·玫瑰",
        "哈利·波特",
        "蜘蛛侠：英雄归来",
        "复仇者联盟：无限战争",
        "变形金刚：超能勇士崛起",
        "加勒比海盗：黑珍珠号的诅咒",
        "指环王：护戒使者",
        "星球大战：原力觉醒",
        "速度与激情：特别行动",
        "X战警：逆转未来",
        "钢铁侠 Iron Man",
        "蜘蛛侠：平行宇宙",
        "阿凡达：水之道",
        "变形金刚5：最后的骑士",
        "哈利·波特与魔法石"
    ]
    
    all_passed = True
    
    for movie_name in movie_names:
        is_valid, error_msg = SecurityValidator.validate_search_keyword(movie_name)
        
        if is_valid:
            print(f"✅ '{movie_name}' - 验证通过")
        else:
            print(f"❌ '{movie_name}' - 验证失败: {error_msg}")
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有电影名称验证通过！")
    else:
        print("⚠️ 部分电影名称验证失败")
    
    return all_passed

if __name__ == "__main__":
    print("🔍 搜索关键词验证测试")
    print("=" * 60)
    
    # 运行基础验证测试
    basic_test_passed = test_search_validation()
    
    # 运行电影名称测试
    movie_test_passed = test_specific_movie_names()
    
    print("\n" + "=" * 60)
    if basic_test_passed and movie_test_passed:
        print("🎉 所有测试通过！搜索验证功能正常")
        sys.exit(0)
    else:
        print("❌ 测试失败，需要修复验证逻辑")
        sys.exit(1)
