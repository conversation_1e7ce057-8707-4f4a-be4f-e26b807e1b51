"""
监控API端点
"""

from fastapi import APIRouter, HTTPException, Request, Query
from fastapi.responses import JSONResponse
import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from monitoring import metrics_collector
from security_logger import security_logger

logger = logging.getLogger(__name__)

# 创建监控路由
monitoring_router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])

@monitoring_router.get("/health")
async def get_health_status():
    """获取应用健康状态"""
    try:
        health_status = metrics_collector.get_health_status()
        
        # 根据健康状态设置HTTP状态码
        status_code = 200
        if health_status['status'] == 'degraded':
            status_code = 200  # 降级但仍可用
        elif health_status['status'] == 'unhealthy':
            status_code = 503  # 服务不可用
        
        return JSONResponse(
            status_code=status_code,
            content={
                "success": True,
                "data": health_status,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"获取健康状态失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": "获取健康状态失败",
                "timestamp": datetime.now().isoformat()
            }
        )

@monitoring_router.get("/stats")
async def get_monitoring_stats(
    time_window: int = Query(3600, description="时间窗口（秒）", ge=60, le=86400)
):
    """获取监控统计信息"""
    try:
        request_stats = metrics_collector.get_request_stats(time_window)
        security_stats = metrics_collector.get_security_stats(time_window)
        system_stats = metrics_collector.get_system_stats()
        
        return {
            "success": True,
            "data": {
                "request_stats": request_stats,
                "security_stats": security_stats,
                "system_stats": system_stats,
                "time_window": time_window,
                "time_window_formatted": f"{time_window // 60} 分钟" if time_window < 3600 else f"{time_window // 3600} 小时"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取监控统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取监控统计失败")

@monitoring_router.get("/alerts")
async def get_alerts():
    """获取告警信息"""
    try:
        health_status = metrics_collector.get_health_status()
        request_stats = metrics_collector.get_request_stats(300)  # 最近5分钟
        security_stats = metrics_collector.get_security_stats(300)
        system_stats = metrics_collector.get_system_stats()
        
        alerts = []
        
        # 生成告警
        if health_status['status'] == 'unhealthy':
            alerts.append({
                "level": "critical",
                "type": "health",
                "message": "应用健康状态异常",
                "details": health_status['issues'],
                "timestamp": datetime.now().isoformat()
            })
        elif health_status['status'] == 'degraded':
            alerts.append({
                "level": "warning",
                "type": "health",
                "message": "应用性能降级",
                "details": health_status['issues'],
                "timestamp": datetime.now().isoformat()
            })
        
        # 错误率告警
        if request_stats['error_rate'] > 5:
            alerts.append({
                "level": "warning" if request_stats['error_rate'] < 10 else "critical",
                "type": "error_rate",
                "message": f"错误率过高: {request_stats['error_rate']}%",
                "details": {"error_rate": request_stats['error_rate']},
                "timestamp": datetime.now().isoformat()
            })
        
        # 响应时间告警
        if request_stats['avg_response_time'] > 3:
            alerts.append({
                "level": "warning" if request_stats['avg_response_time'] < 5 else "critical",
                "type": "response_time",
                "message": f"响应时间过长: {request_stats['avg_response_time']}s",
                "details": {"avg_response_time": request_stats['avg_response_time']},
                "timestamp": datetime.now().isoformat()
            })
        
        # 攻击频率告警
        if security_stats['attacks_per_hour'] > 50:
            alerts.append({
                "level": "warning" if security_stats['attacks_per_hour'] < 100 else "critical",
                "type": "security",
                "message": f"攻击频率过高: {security_stats['attacks_per_hour']}/小时",
                "details": security_stats,
                "timestamp": datetime.now().isoformat()
            })
        
        # 系统资源告警
        if system_stats['cpu_percent'] > 80:
            alerts.append({
                "level": "warning" if system_stats['cpu_percent'] < 90 else "critical",
                "type": "system",
                "message": f"CPU使用率过高: {system_stats['cpu_percent']}%",
                "details": {"cpu_percent": system_stats['cpu_percent']},
                "timestamp": datetime.now().isoformat()
            })
        
        if system_stats['memory_percent'] > 85:
            alerts.append({
                "level": "warning" if system_stats['memory_percent'] < 95 else "critical",
                "type": "system",
                "message": f"内存使用率过高: {system_stats['memory_percent']}%",
                "details": {"memory_percent": system_stats['memory_percent']},
                "timestamp": datetime.now().isoformat()
            })
        
        # 按严重程度排序
        alert_priority = {"critical": 3, "warning": 2, "info": 1}
        alerts.sort(key=lambda x: alert_priority.get(x['level'], 0), reverse=True)
        
        return {
            "success": True,
            "data": {
                "alerts": alerts,
                "alert_count": len(alerts),
                "critical_count": len([a for a in alerts if a['level'] == 'critical']),
                "warning_count": len([a for a in alerts if a['level'] == 'warning'])
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取告警信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取告警信息失败")

@monitoring_router.get("/metrics/export")
async def export_metrics(
    format: str = Query("json", description="导出格式", regex="^(json|csv)$")
):
    """导出监控指标"""
    try:
        if format == "json":
            metrics_data = metrics_collector.export_metrics("json")
            return JSONResponse(
                content=metrics_data,
                media_type="application/json",
                headers={"Content-Disposition": f"attachment; filename=metrics_{int(time.time())}.json"}
            )
        else:
            # CSV格式导出（简化版本）
            request_stats = metrics_collector.get_request_stats()
            csv_data = "timestamp,total_requests,error_rate,avg_response_time\n"
            csv_data += f"{datetime.now().isoformat()},{request_stats['total_requests']},{request_stats['error_rate']},{request_stats['avg_response_time']}\n"
            
            return JSONResponse(
                content=csv_data,
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=metrics_{int(time.time())}.csv"}
            )
            
    except Exception as e:
        logger.error(f"导出监控指标失败: {e}")
        raise HTTPException(status_code=500, detail="导出监控指标失败")

@monitoring_router.get("/logs/recent")
async def get_recent_logs(
    level: str = Query("INFO", description="日志级别"),
    limit: int = Query(100, description="返回条数", ge=1, le=1000)
):
    """获取最近的日志（简化版本）"""
    try:
        # 这里应该从实际的日志文件或日志系统中读取
        # 为了演示，返回模拟数据
        logs = [
            {
                "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat(),
                "level": "INFO" if i % 3 != 0 else "WARNING",
                "message": f"示例日志消息 {i}",
                "module": "main" if i % 2 == 0 else "security"
            }
            for i in range(min(limit, 50))
        ]
        
        return {
            "success": True,
            "data": {
                "logs": logs,
                "count": len(logs),
                "level_filter": level
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        raise HTTPException(status_code=500, detail="获取日志失败")

@monitoring_router.post("/reset")
async def reset_monitoring_stats(request: Request):
    """重置监控统计（管理员功能）"""
    try:
        # 记录重置操作
        client_ip = request.client.host
        logger.warning(f"监控统计被重置 - IP: {client_ip}")
        
        # 重置统计数据
        metrics_collector.reset_stats()
        
        return {
            "success": True,
            "message": "监控统计已重置",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"重置监控统计失败: {e}")
        raise HTTPException(status_code=500, detail="重置监控统计失败")

@monitoring_router.get("/dashboard")
async def get_dashboard_data():
    """获取监控仪表板数据"""
    try:
        # 获取各种时间窗口的数据
        stats_5min = metrics_collector.get_request_stats(300)
        stats_1hour = metrics_collector.get_request_stats(3600)
        stats_24hour = metrics_collector.get_request_stats(86400)
        
        security_stats = metrics_collector.get_security_stats(3600)
        system_stats = metrics_collector.get_system_stats()
        health_status = metrics_collector.get_health_status()
        
        # 构建仪表板数据
        dashboard_data = {
            "overview": {
                "status": health_status['status'],
                "uptime": health_status['uptime_formatted'],
                "total_requests": health_status['stats_summary']['total_requests'],
                "total_errors": health_status['stats_summary']['total_errors'],
                "total_attacks": health_status['stats_summary']['total_attacks']
            },
            "performance": {
                "current_error_rate": stats_5min['error_rate'],
                "current_response_time": stats_5min['avg_response_time'],
                "requests_per_minute": stats_5min['requests_per_minute'],
                "trend": {
                    "5min": stats_5min,
                    "1hour": stats_1hour,
                    "24hour": stats_24hour
                }
            },
            "security": {
                "recent_attacks": security_stats['recent_attacks'],
                "blocked_ips": security_stats['blocked_ips_count'],
                "attack_types": security_stats['attack_types'],
                "attacks_per_hour": security_stats['attacks_per_hour']
            },
            "system": {
                "cpu_percent": system_stats['cpu_percent'],
                "memory_percent": system_stats['memory_percent'],
                "memory_used_mb": system_stats['memory_used_mb'],
                "disk_usage_percent": system_stats['disk_usage_percent'],
                "active_connections": system_stats['active_connections']
            },
            "top_endpoints": stats_1hour['top_endpoints'],
            "slow_requests": stats_1hour['slow_requests'][:5],
            "recent_issues": health_status['issues']
        }
        
        return {
            "success": True,
            "data": dashboard_data,
            "timestamp": datetime.now().isoformat(),
            "refresh_interval": 30  # 建议刷新间隔（秒）
        }
        
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取仪表板数据失败")