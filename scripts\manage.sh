#!/bin/bash

# 应用管理脚本
# 提供常用的管理操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SERVICE_NAME="movie-search"
APP_DIR="/opt/movie-search"
APP_USER="movieapp"

# 显示使用说明
show_usage() {
    echo "移动端电影搜索应用 - 管理脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 <命令> [选项]"
    echo ""
    echo "可用命令:"
    echo "  start           启动应用服务"
    echo "  stop            停止应用服务"
    echo "  restart         重启应用服务"
    echo "  status          查看服务状态"
    echo "  logs            查看应用日志"
    echo "  health          检查应用健康状态"
    echo "  update          更新应用代码"
    echo "  backup          备份应用数据"
    echo "  restore         恢复应用数据"
    echo "  clean           清理日志和缓存"
    echo "  monitor         实时监控"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 logs -f"
    echo "  $0 backup"
    echo ""
}

# 启动服务
start_service() {
    log_info "启动应用服务..."
    sudo systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 3
    
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
        show_service_status
    else
        log_error "服务启动失败"
        sudo systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止应用服务..."
    sudo systemctl stop "$SERVICE_NAME"
    
    if ! sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务已停止"
    else
        log_error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启应用服务..."
    sudo systemctl restart "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 3
    
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务重启成功"
        show_service_status
    else
        log_error "服务重启失败"
        sudo systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

# 查看服务状态
show_service_status() {
    log_info "服务状态信息："
    sudo systemctl status "$SERVICE_NAME" --no-pager -l
    
    echo ""
    log_info "端口监听状态："
    netstat -tlnp | grep :8506 || echo "端口8506未监听"
    
    echo ""
    log_info "进程信息："
    ps aux | grep -E "(uvicorn|python.*main)" | grep -v grep || echo "未找到应用进程"
}

# 查看日志
show_logs() {
    local follow_flag=""
    if [[ "$1" == "-f" || "$1" == "--follow" ]]; then
        follow_flag="-f"
    fi
    
    log_info "应用日志："
    if [ -n "$follow_flag" ]; then
        sudo journalctl -u "$SERVICE_NAME" -f
    else
        sudo journalctl -u "$SERVICE_NAME" --no-pager -l -n 50
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查服务状态
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "✓ 系统服务运行正常"
    else
        log_error "✗ 系统服务未运行"
        return 1
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":8506 "; then
        log_success "✓ 应用端口监听正常"
    else
        log_error "✗ 应用端口未监听"
        return 1
    fi
    
    # 检查HTTP响应
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost:8506/health" | grep -q "200"; then
        log_success "✓ 应用HTTP响应正常"
    else
        log_error "✗ 应用HTTP响应异常"
        return 1
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df "$APP_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        log_success "✓ 磁盘空间充足 (${disk_usage}%)"
    else
        log_warning "⚠ 磁盘空间不足 (${disk_usage}%)"
    fi
    
    # 检查内存使用
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -lt 90 ]; then
        log_success "✓ 内存使用正常 (${mem_usage}%)"
    else
        log_warning "⚠ 内存使用较高 (${mem_usage}%)"
    fi
    
    log_success "健康检查完成"
}

# 更新应用
update_app() {
    log_info "更新应用代码..."
    
    # 备份当前版本
    local backup_dir="/tmp/movie-search-backup-$(date +%Y%m%d_%H%M%S)"
    sudo cp -r "$APP_DIR" "$backup_dir"
    log_info "当前版本已备份到: $backup_dir"
    
    # 停止服务
    stop_service
    
    # 更新代码（这里需要根据实际情况修改）
    log_info "请手动更新代码文件到 $APP_DIR"
    log_info "更新完成后按回车继续..."
    read
    
    # 安装依赖
    log_info "更新Python依赖..."
    sudo -u "$APP_USER" "$APP_DIR/venv/bin/pip" install -r "$APP_DIR/requirements.txt"
    
    # 重启服务
    start_service
    
    log_success "应用更新完成"
}

# 备份数据
backup_data() {
    log_info "备份应用数据..."
    
    local backup_dir="${BACKUP_DIR:-/opt/backups}/movie-search"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$backup_dir/backup_$timestamp.tar.gz"
    
    # 创建备份目录
    sudo mkdir -p "$backup_dir"
    
    # 创建备份
    sudo tar -czf "$backup_file" \
        -C "$(dirname "$APP_DIR")" \
        --exclude="$(basename "$APP_DIR")/venv" \
        --exclude="$(basename "$APP_DIR")/logs/*.log" \
        "$(basename "$APP_DIR")"
    
    log_success "备份完成: $backup_file"
    
    # 清理旧备份（保留最近7个）
    sudo find "$backup_dir" -name "backup_*.tar.gz" -type f -mtime +7 -delete
    
    # 显示备份信息
    log_info "备份文件列表："
    sudo ls -lh "$backup_dir"/backup_*.tar.gz 2>/dev/null || echo "无备份文件"
}

# 恢复数据
restore_data() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        log_info "使用方法: $0 restore /path/to/backup.tar.gz"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_warning "即将恢复数据，这将覆盖当前应用文件"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复已取消"
        exit 0
    fi
    
    # 停止服务
    stop_service
    
    # 备份当前状态
    local current_backup="/tmp/current_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    sudo tar -czf "$current_backup" -C "$(dirname "$APP_DIR")" "$(basename "$APP_DIR")"
    log_info "当前状态已备份到: $current_backup"
    
    # 恢复数据
    log_info "恢复数据..."
    sudo tar -xzf "$backup_file" -C "$(dirname "$APP_DIR")"
    
    # 重新安装依赖
    log_info "重新安装依赖..."
    sudo -u "$APP_USER" "$APP_DIR/venv/bin/pip" install -r "$APP_DIR/requirements.txt"
    
    # 启动服务
    start_service
    
    log_success "数据恢复完成"
}

# 清理日志和缓存
clean_data() {
    log_info "清理日志和缓存..."
    
    # 清理应用日志
    sudo find "$APP_DIR/logs" -name "*.log" -type f -mtime +30 -delete
    sudo find "$APP_DIR/logs" -name "*.log.*" -type f -mtime +7 -delete
    
    # 清理Python缓存
    sudo find "$APP_DIR" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    sudo find "$APP_DIR" -name "*.pyc" -type f -delete 2>/dev/null || true
    
    # 清理临时文件
    sudo find /tmp -name "*movie-search*" -type f -mtime +1 -delete 2>/dev/null || true
    
    log_success "清理完成"
}

# 实时监控
monitor_app() {
    log_info "启动实时监控 (按Ctrl+C退出)..."
    
    while true; do
        clear
        echo "=== 移动端电影搜索应用 - 实时监控 ==="
        echo "时间: $(date)"
        echo ""
        
        # 服务状态
        if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
            echo -e "服务状态: ${GREEN}运行中${NC}"
        else
            echo -e "服务状态: ${RED}已停止${NC}"
        fi
        
        # 进程信息
        local pid=$(pgrep -f "uvicorn.*main:app" | head -1)
        if [ -n "$pid" ]; then
            echo "进程ID: $pid"
            local cpu_mem=$(ps -p "$pid" -o %cpu,%mem --no-headers)
            echo "CPU/内存: $cpu_mem"
        fi
        
        # 端口监听
        if netstat -tlnp | grep -q ":8506 "; then
            echo -e "端口8506: ${GREEN}监听中${NC}"
        else
            echo -e "端口8506: ${RED}未监听${NC}"
        fi
        
        # 最近日志
        echo ""
        echo "=== 最近日志 ==="
        sudo journalctl -u "$SERVICE_NAME" --no-pager -n 5 --since "1 minute ago" | tail -5
        
        sleep 5
    done
}

# 主函数
main() {
    case "${1:-}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_service_status
            ;;
        logs)
            show_logs "$2"
            ;;
        health)
            health_check
            ;;
        update)
            update_app
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$2"
            ;;
        clean)
            clean_data
            ;;
        monitor)
            monitor_app
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
