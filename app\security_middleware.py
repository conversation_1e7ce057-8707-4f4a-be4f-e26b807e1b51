"""
安全中间件
"""

import time
import logging
import hashlib
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from security_config import security_config
from security import rate_limiter
from typing import Callable, Optional
import re
import ipaddress

logger = logging.getLogger(__name__)

class SecurityMiddleware:
    """安全中间件类"""
    
    def __init__(self):
        self.blocked_ips = set()
        self.suspicious_requests = {}
        
    async def __call__(self, request: Request, call_next: Callable):
        """中间件主要逻辑"""
        start_time = time.time()
        client_id = self.get_client_identifier(request)
        client_ip = self.get_client_ip(request)
        
        try:
            # 1. IP黑名单检查
            if self.is_ip_blocked(client_ip):
                logger.warning(f"阻止黑名单IP访问: {client_ip}")
                return self.create_error_response(403, "访问被拒绝")
            
            # 2. 用户代理检查
            if security_config.ENABLE_USER_AGENT_FILTERING:
                if not self.validate_user_agent(request):
                    logger.warning(f"阻止可疑用户代理: {client_ip} - {request.headers.get('user-agent', 'Unknown')}")
                    return self.create_error_response(403, "访问被拒绝")
            
            # 3. 蜜罐检查
            if security_config.ENABLE_HONEYPOT:
                if self.is_honeypot_request(request):
                    logger.warning(f"蜜罐触发: {client_ip} - {request.url.path}")
                    self.block_ip(client_ip)
                    return self.create_error_response(404, "页面不存在")
            
            # 4. 请求大小检查
            if not self.validate_request_size(request):
                logger.warning(f"请求过大: {client_ip}")
                return self.create_error_response(413, "请求实体过大")
            
            # 5. 路径遍历检查
            if self.detect_path_traversal(request):
                logger.warning(f"路径遍历攻击: {client_ip} - {request.url.path}")
                return self.create_error_response(400, "非法请求")
            
            # 6. 可疑请求模式检查
            if self.detect_suspicious_patterns(request, client_id):
                logger.warning(f"可疑请求模式: {client_id}")
                return self.create_error_response(429, "请求过于频繁")
            
            # 执行请求
            response = await call_next(request)
            
            # 7. 添加安全响应头
            self.add_security_headers(response)
            
            # 8. 记录请求信息
            process_time = time.time() - start_time
            self.log_request(request, response, process_time, client_id)
            
            return response
            
        except Exception as e:
            logger.error(f"安全中间件异常: {e}")
            return self.create_error_response(500, "服务器内部错误")
    
    def get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符"""
        client_ip = self.get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # 创建更唯一的客户端标识
        identifier = f"{client_ip}:{hashlib.md5(user_agent.encode()).hexdigest()[:8]}"
        return identifier
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        # 检查代理头部
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # 取第一个IP（最原始的客户端IP）
            ip = forwarded_for.split(",")[0].strip()
            if self.is_valid_ip(ip):
                return ip
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip and self.is_valid_ip(real_ip):
            return real_ip
        
        # 回退到直接连接IP
        return request.client.host if request.client else "unknown"
    
    def is_valid_ip(self, ip: str) -> bool:
        """验证IP地址格式"""
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    def is_ip_blocked(self, ip: str) -> bool:
        """检查IP是否被阻止"""
        return ip in self.blocked_ips
    
    def block_ip(self, ip: str):
        """阻止IP"""
        self.blocked_ips.add(ip)
        logger.warning(f"IP已被加入黑名单: {ip}")
    
    def validate_user_agent(self, request: Request) -> bool:
        """验证用户代理"""
        user_agent = request.headers.get("user-agent", "").lower()
        
        # 检查是否为空或过短
        if not user_agent or len(user_agent) < 10:
            return False
        
        # 检查是否包含被阻止的关键词
        for blocked_ua in security_config.BLOCKED_USER_AGENTS:
            if blocked_ua in user_agent:
                return False
        
        return True
    
    def is_honeypot_request(self, request: Request) -> bool:
        """检查是否为蜜罐请求"""
        path = request.url.path.lower()
        return any(honeypot in path for honeypot in security_config.HONEYPOT_ENDPOINTS)
    
    def validate_request_size(self, request: Request) -> bool:
        """验证请求大小"""
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                # 限制请求体大小为1MB
                return size <= 1024 * 1024
            except ValueError:
                return False
        return True
    
    def detect_path_traversal(self, request: Request) -> bool:
        """检测路径遍历攻击"""
        path = request.url.path
        query = str(request.query_params)
        
        # 路径遍历模式
        traversal_patterns = [
            r'\.\./+',
            r'\.\.\\+',
            r'/etc/passwd',
            r'/etc/shadow',
            r'\\windows\\system32',
            r'\\boot\.ini',
        ]
        
        full_request = f"{path}?{query}"
        for pattern in traversal_patterns:
            if re.search(pattern, full_request, re.IGNORECASE):
                return True
        
        return False
    
    def detect_suspicious_patterns(self, request: Request, client_id: str) -> bool:
        """检测可疑请求模式"""
        now = time.time()
        
        if client_id not in self.suspicious_requests:
            self.suspicious_requests[client_id] = {
                'requests': [],
                'paths': [],
                'last_check': now
            }
        
        client_data = self.suspicious_requests[client_id]
        client_data['requests'].append(now)
        client_data['paths'].append(request.url.path)
        client_data['last_check'] = now
        
        # 清理过期数据（保留最近5分钟）
        cutoff_time = now - 300
        client_data['requests'] = [t for t in client_data['requests'] if t > cutoff_time]
        client_data['paths'] = client_data['paths'][-50:]  # 保留最近50个路径
        
        # 检查请求频率
        if len(client_data['requests']) > 100:  # 5分钟内超过100个请求
            return True
        
        # 检查路径扫描行为
        unique_paths = set(client_data['paths'])
        if len(unique_paths) > 20 and len(client_data['paths']) > 30:  # 访问了很多不同路径
            return True
        
        return False
    
    def add_security_headers(self, response):
        """添加安全响应头"""
        # 基本安全头
        for header, value in security_config.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        # CSP头
        response.headers["Content-Security-Policy"] = security_config.get_csp_header_value()
    
    def log_request(self, request: Request, response, process_time: float, client_id: str):
        """记录请求日志"""
        if security_config.LOG_SECURITY_EVENTS:
            status_code = getattr(response, 'status_code', 200)
            
            # 记录基本信息
            logger.info(
                f"请求: {request.method} {request.url.path} - "
                f"状态: {status_code} - "
                f"耗时: {process_time:.3f}s - "
                f"客户端: {client_id}"
            )
            
            # 记录慢请求
            if process_time > security_config.SLOW_REQUEST_THRESHOLD:
                logger.warning(
                    f"慢请求: {request.method} {request.url.path} - "
                    f"耗时: {process_time:.3f}s - "
                    f"客户端: {client_id}"
                )
    
    def create_error_response(self, status_code: int, message: str) -> JSONResponse:
        """创建错误响应"""
        return JSONResponse(
            status_code=status_code,
            content={
                "success": False,
                "error": {
                    "code": status_code,
                    "message": message,
                    "type": "security_error"
                },
                "timestamp": time.time()
            }
        )
    
    def cleanup_expired_data(self):
        """清理过期数据"""
        now = time.time()
        cutoff_time = now - 3600  # 1小时前
        
        # 清理可疑请求记录
        expired_clients = []
        for client_id, data in self.suspicious_requests.items():
            if data['last_check'] < cutoff_time:
                expired_clients.append(client_id)
        
        for client_id in expired_clients:
            del self.suspicious_requests[client_id]
        
        logger.info(f"清理安全中间件数据: 删除 {len(expired_clients)} 个过期记录")

# 创建全局中间件实例
security_middleware = SecurityMiddleware()