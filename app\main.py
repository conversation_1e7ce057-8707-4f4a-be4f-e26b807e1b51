"""
移动端电影搜索应用 - FastAPI主应用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, ValidationError
import uvicorn
import logging
from datetime import datetime
import threading
import time
import traceback
import os
from typing import Union

# 导入自定义模块
from modules.wps_searcher import search_content
from modules.maoyan_hot import get_maoyan_hot_shows, get_default_hot_movies
from modules.link_validator import validate_link
from modules.quark_info import get_quark_folder_info
from modules.cloudsave_searcher import search_cloudsave
from security import security_validator, rate_limiter
from security_config import security_config
from security_middleware import security_middleware
from monitoring import metrics_collector
from monitoring_api import monitoring_router
from logging_config import setup_logging, request_logger, performance_logger
from api_auth import api_authenticator, link_encryptor
from log_sanitizer import get_sanitized_logger, access_control_logger
from session_manager import session_manager

# 设置日志配置
setup_logging(
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    log_dir=os.getenv("LOG_DIR", "logs")
)

# 使用脱敏日志记录器
logger = get_sanitized_logger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="移动端电影搜索应用",
    description="基于WPS内容搜索和猫眼热门数据的电影资源搜索应用",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加监控路由
app.include_router(monitoring_router)

# 配置安全的CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=security_config.CORS_ORIGINS,
    allow_credentials=security_config.CORS_CREDENTIALS,
    allow_methods=security_config.ALLOWED_METHODS,
    allow_headers=security_config.ALLOWED_HEADERS,
    expose_headers=security_config.EXPOSE_HEADERS,
    max_age=security_config.MAX_AGE,
)

# 增强的请求频率限制系统
from collections import defaultdict
import time
import hashlib

# 存储客户端请求记录
client_requests = defaultdict(list)
client_violations = defaultdict(int)  # 记录违规次数
blocked_ips = {}  # 被阻止的IP及解封时间

# 导入统一配置
from security_config import security_config

# 使用配置文件中的值，不再硬编码
RATE_LIMITS = security_config.RATE_LIMITS
MAX_VIOLATIONS = security_config.MAX_VIOLATIONS
BLOCK_DURATION = security_config.BLOCK_DURATION

def get_client_identifier(request: Request) -> str:
    """获取客户端标识符（结合IP和User-Agent）"""
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "")
    
    # 创建更唯一的客户端标识
    identifier = f"{client_ip}:{hashlib.md5(user_agent.encode()).hexdigest()[:8]}"
    return identifier

def check_rate_limit(client_id: str, endpoint: str = "default") -> tuple[bool, str]:
    """
    检查客户端是否超过请求频率限制
    
    Args:
        client_id: 客户端标识符
        endpoint: 接口类型
        
    Returns:
        tuple[bool, str]: (是否允许, 错误信息)
    """
    now = time.time()
    
    # 获取该接口的限制配置
    limits = RATE_LIMITS.get(endpoint, RATE_LIMITS["default"])
    rate_limit_requests = limits["requests"]
    rate_limit_window = limits["window"]
    burst_limit = limits["burst"]
    burst_window = limits["burst_window"]
    
    # 检查是否在黑名单中
    if client_id in blocked_ips:
        if now < blocked_ips[client_id]:
            remaining_time = int(blocked_ips[client_id] - now)
            return False, f"客户端已被临时阻止，剩余时间: {remaining_time}秒"
        else:
            # 解除阻止
            del blocked_ips[client_id]
            client_violations[client_id] = 0
    
    # 为不同接口创建独立的请求记录
    endpoint_key = f"{client_id}:{endpoint}"
    
    # 清理过期的请求记录
    client_requests[endpoint_key] = [
        req_time for req_time in client_requests[endpoint_key]
        if now - req_time < rate_limit_window
    ]
    
    # 检查短时间内的突发请求
    recent_requests = [
        req_time for req_time in client_requests[endpoint_key]
        if now - req_time < burst_window
    ]
    
    if len(recent_requests) >= burst_limit:
        client_violations[client_id] += 1
        logger.warning(f"客户端 {client_id} 在接口 {endpoint} 触发突发请求限制，违规次数: {client_violations[client_id]}")
        
        if client_violations[client_id] >= MAX_VIOLATIONS:
            # 临时阻止该客户端
            blocked_ips[client_id] = now + BLOCK_DURATION
            logger.error(f"客户端 {client_id} 因频繁违规被临时阻止 {BLOCK_DURATION} 秒")
            return False, f"因频繁请求被临时阻止 {BLOCK_DURATION} 秒"
        
        return False, f"请求过于频繁，请等待 {burst_window} 秒后重试"
    
    # 检查常规频率限制
    if len(client_requests[endpoint_key]) >= rate_limit_requests:
        return False, f"该接口每分钟最多允许 {rate_limit_requests} 次请求"
    
    # 记录当前请求
    client_requests[endpoint_key].append(now)
    return True, ""

def cleanup_rate_limit_data():
    """清理过期的频率限制数据"""
    now = time.time()
    
    # 清理过期的请求记录
    expired_keys = []
    for endpoint_key, requests in client_requests.items():
        # 使用最长的时间窗口进行清理
        max_window = max(limits["window"] for limits in RATE_LIMITS.values())
        client_requests[endpoint_key] = [
            req_time for req_time in requests
            if now - req_time < max_window
        ]
        if not client_requests[endpoint_key]:
            expired_keys.append(endpoint_key)
    
    for endpoint_key in expired_keys:
        del client_requests[endpoint_key]
    
    # 清理过期的违规记录
    expired_violations = []
    for client_id, violation_count in client_violations.items():
        # 如果客户端没有任何活跃请求，清理违规记录
        has_active_requests = any(
            endpoint_key.startswith(f"{client_id}:") 
            for endpoint_key in client_requests.keys()
        )
        if not has_active_requests:
            expired_violations.append(client_id)
    
    for client_id in expired_violations:
        del client_violations[client_id]
    
    # 清理过期的阻止记录
    expired_blocks = [
        client_id for client_id, unblock_time in blocked_ips.items()
        if now >= unblock_time
    ]
    for client_id in expired_blocks:
        del blocked_ips[client_id]
    
    logger.info(f"清理频率限制数据: 删除 {len(expired_keys)} 个过期请求记录, {len(expired_violations)} 个违规记录, {len(expired_blocks)} 个阻止记录")

# 挂载静态文件服务
# 修正路径，从app目录向上一级找static目录
static_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 添加监控路由
app.include_router(monitoring_router)

# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常 {exc.status_code}: {exc.detail} - 请求路径: {request.url}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": exc.status_code,
                "message": exc.detail,
                "type": "http_error"
            },
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"请求验证失败: {exc.errors()} - 请求路径: {request.url}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "success": False,
            "error": {
                "code": 422,
                "message": "请求参数验证失败",
                "details": exc.errors(),
                "type": "validation_error"
            },
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    error_id = f"error_{int(time.time())}"
    logger.error(f"未处理异常 [{error_id}]: {str(exc)} - 请求路径: {request.url}")
    logger.error(f"异常详情 [{error_id}]: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": {
                "code": 500,
                "message": "服务器内部错误，请稍后重试",
                "error_id": error_id,
                "type": "internal_error"
            },
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

# API认证中间件
@app.middleware("http")
async def api_auth_middleware(request: Request, call_next):
    """API认证中间件"""
    # 检查是否需要认证
    path = request.url.path

    # 定义需要会话验证的API端点
    session_required_endpoints = [
        "/api/search",
        "/api/search-cloudsave",
        "/api/decrypt-link"
    ]

    # 定义免认证的API端点
    public_endpoints = [
        "/api/hot-movies",
        "/api/session",
        "/health",
        "/docs",
        "/redoc"
    ]

    # 检查是否需要会话验证
    needs_session = False
    for endpoint in session_required_endpoints:
        if path.startswith(endpoint):
            needs_session = True
            break

    # 如果不需要会话验证，直接通过
    if not needs_session:
        # 为公共端点设置默认用户信息
        if any(path.startswith(ep) for ep in public_endpoints):
            request.state.username = "Public"
        return await call_next(request)

    # 需要会话验证的端点
    try:
        # 获取会话验证所需的头部信息
        session_token = request.headers.get("X-Session-Token")
        browser_fingerprint = request.headers.get("X-Browser-Fingerprint")
        client_ip = request.client.host

        # 验证会话
        is_valid, error_msg = session_manager.validate_session(
            session_token, browser_fingerprint, client_ip
        )

        if not is_valid:
            # 记录会话验证失败日志
            access_control_logger.log_api_access(
                client_ip=client_ip,
                api_key=session_token[:8] + "..." if session_token else "None",
                username="Unknown",
                endpoint=request.url.path,
                success=False,
                error_msg=error_msg
            )

            raise HTTPException(status_code=401, detail=error_msg)

        # 检测可疑活动
        if session_manager.is_suspicious_activity(browser_fingerprint):
            logger.warning(f"检测到可疑活动: 指纹 {browser_fingerprint[:8]}... from IP {client_ip}")
            # 可以选择阻止或要求额外验证

        # 记录会话验证成功日志
        access_control_logger.log_api_access(
            client_ip=client_ip,
            api_key=session_token[:8] + "..." if session_token else "None",
            username="Session User",
            endpoint=request.url.path,
            success=True
        )

        # 将用户信息添加到请求状态中
        request.state.username = "Session User"
        request.state.fingerprint = browser_fingerprint

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"会话验证中间件异常: {e}")
        raise HTTPException(status_code=500, detail="会话验证服务异常")

    return await call_next(request)

# 安全中间件
@app.middleware("http")
async def security_middleware_handler(request: Request, call_next):
    """安全中间件处理器"""
    return await security_middleware(request, call_next)

# 请求日志和性能监控中间件
@app.middleware("http")
async def log_and_monitor_requests(request: Request, call_next):
    """请求日志和性能监控中间件"""
    start_time = time.time()
    client_id = get_client_identifier(request)
    user_agent = request.headers.get("user-agent", "")
    
    # 记录请求开始
    logger.info(f"请求开始: {request.method} {request.url} - 客户端: {client_id}")
    
    try:
        response = await call_next(request)
        
        # 记录请求完成
        process_time = time.time() - start_time
        logger.info(f"请求完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}s")
        
        # 记录到监控系统
        metrics_collector.record_request(
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            response_time=process_time,
            client_id=client_id,
            user_agent=user_agent
        )
        
        # 记录访问日志
        request_logger.log_request(
            method=request.method,
            path=str(request.url.path),
            status_code=response.status_code,
            response_time=process_time,
            client_ip=request.client.host if request.client else "unknown",
            user_agent=user_agent,
            request_id=f"req_{int(start_time)}"
        )
        
        # 记录慢请求
        request_logger.log_slow_request(
            method=request.method,
            path=str(request.url.path),
            response_time=process_time,
            threshold=5.0
        )
        
        # 添加性能和追踪响应头
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Request-ID"] = f"req_{int(start_time)}"
        response.headers["X-Client-ID"] = hashlib.md5(client_id.encode()).hexdigest()[:16]
        
        return response
        
    except Exception as e:
        # 记录请求异常
        process_time = time.time() - start_time
        logger.error(f"请求异常: {request.method} {request.url} - 异常: {str(e)} - 耗时: {process_time:.3f}s")
        
        # 记录异常到监控系统
        metrics_collector.record_request(
            method=request.method,
            path=request.url.path,
            status_code=500,
            response_time=process_time,
            client_id=client_id,
            user_agent=user_agent,
            error_message=str(e)
        )
        
        raise

# 全局变量存储热门电影数据
hot_movies = []
last_update_time = 0
update_interval = 24 * 3600  # 24小时更新一次
startup_time = time.time()  # 记录启动时间

# 请求模型
class SearchRequest(BaseModel):
    keyword: str

class DecryptLinkRequest(BaseModel):
    encrypted_link: str

class SessionRequest(BaseModel):
    fingerprint: str
    timestamp: int

@app.post("/api/session")
async def create_session(request: SessionRequest, http_request: Request):
    """创建会话令牌接口"""
    try:
        fingerprint = request.fingerprint
        client_ip = http_request.client.host

        # 基本验证
        if not fingerprint or len(fingerprint) < 8:
            raise HTTPException(status_code=400, detail="无效的浏览器指纹")

        # 检查时间戳（防止重放攻击）
        current_time = time.time() * 1000  # 转换为毫秒
        if abs(current_time - request.timestamp) > 60000:  # 1分钟内有效
            raise HTTPException(status_code=400, detail="请求时间戳无效")

        # 创建会话
        token, expires_in = session_manager.create_session(fingerprint, client_ip)

        logger.info(f"创建会话成功: IP {client_ip}")

        return {
            "success": True,
            "token": token,
            "expires_in": expires_in,
            "message": "会话创建成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建会话异常: {e}")
        raise HTTPException(status_code=500, detail="会话服务异常")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页面"""
    try:
        # 修正路径，从app目录向上一级找static目录
        static_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "static", "index.html")
        with open(static_path, "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>页面正在建设中...</h1>")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查各个组件的健康状态
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "mobile-movie-search-app",
            "version": "1.0.0",
            "uptime": time.time() - startup_time if 'startup_time' in globals() else 0,
            "components": {
                "hot_movies": {
                    "status": "healthy" if hot_movies else "degraded",
                    "count": len(hot_movies),
                    "last_update": datetime.fromtimestamp(last_update_time).isoformat() if last_update_time else None
                },
                "search_service": {
                    "status": "healthy"  # 可以添加更多检查
                },
                "static_files": {
                    "status": "healthy"
                }
            }
        }
        
        # 检查是否有组件处于降级状态
        degraded_components = [
            name for name, component in health_status["components"].items() 
            if component["status"] == "degraded"
        ]
        
        if degraded_components:
            health_status["status"] = "degraded"
            health_status["degraded_components"] = degraded_components
        
        return health_status
        
    except Exception as e:
        logger.error(f"健康检查异常: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "service": "mobile-movie-search-app",
                "error": str(e)
            }
        )

@app.get("/health/ready")
async def readiness_check():
    """就绪检查接口"""
    try:
        # 检查应用是否准备好接收请求
        ready = True
        checks = {}
        
        # 检查热门电影数据是否已加载
        if not hot_movies:
            ready = False
            checks["hot_movies"] = "not_ready"
        else:
            checks["hot_movies"] = "ready"
        
        # 检查静态文件是否存在
        import os
        if not os.path.exists("static/index.html"):
            ready = False
            checks["static_files"] = "not_ready"
        else:
            checks["static_files"] = "ready"
        
        status_code = 200 if ready else 503
        
        return JSONResponse(
            status_code=status_code,
            content={
                "ready": ready,
                "timestamp": datetime.now().isoformat(),
                "checks": checks
            }
        )
        
    except Exception as e:
        logger.error(f"就绪检查异常: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "ready": False,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )

@app.get("/health/live")
async def liveness_check():
    """存活检查接口"""
    return {
        "alive": True,
        "timestamp": datetime.now().isoformat(),
        "pid": os.getpid() if 'os' in globals() else None
    }

@app.get("/api/hot-movies")
async def get_hot_movies(request: Request):
    """获取热门电影接口（公共接口，无需认证）"""
    global hot_movies, last_update_time

    # 获取客户端标识符并检查频率限制（公共接口使用更宽松的限制）
    client_id = get_client_identifier(request)
    rate_limit_ok, rate_limit_msg = check_rate_limit(client_id, "hot-movies")
    if not rate_limit_ok:
        logger.warning(f"热门电影接口频率限制触发: {client_id} - {rate_limit_msg}")
        raise HTTPException(status_code=429, detail=rate_limit_msg)
    
    try:
        # 如果数据为空或超过更新间隔，重新获取
        current_time = time.time()
        if not hot_movies or (current_time - last_update_time) > update_interval:
            logger.info("API请求触发热门电影数据更新...")
            new_movies = get_maoyan_hot_shows()
            if new_movies:
                hot_movies = new_movies
                last_update_time = current_time
                logger.info(f"热门电影数据更新成功，共{len(hot_movies)}部电影")
            else:
                logger.warning("获取新的热门电影数据失败，使用现有数据或默认数据")
                if not hot_movies:
                    hot_movies = get_default_hot_movies()
                    last_update_time = current_time
        
        # 确保返回15条数据
        movies_to_return = hot_movies[:15] if hot_movies else get_default_hot_movies()[:15]
        
        return {
            "success": True,
            "data": movies_to_return,
            "update_time": datetime.fromtimestamp(last_update_time).isoformat() if last_update_time else datetime.now().isoformat(),
            "count": len(movies_to_return),
            "message": "获取热门电影成功"
        }
    except Exception as e:
        logger.error(f"获取热门电影API异常: {e}")
        # 返回默认数据，确保API始终可用
        default_movies = get_default_hot_movies()[:15]
        return {
            "success": True,
            "data": default_movies,
            "update_time": datetime.now().isoformat(),
            "count": len(default_movies),
            "message": "使用默认热门电影数据",
            "fallback": True
        }

@app.post("/api/search")
async def search_movies(request: SearchRequest, http_request: Request):
    """搜索电影接口（需要API密钥认证，返回加密链接）"""
    try:
        # 获取客户端标识符
        client_id = get_client_identifier(http_request)
        client_ip = http_request.client.host
        
        keyword = request.keyword.strip()
        
        # 使用严格的频率限制检查（1次/分钟）
        rate_limit_ok, rate_limit_msg = check_rate_limit(client_id, "search")
        if not rate_limit_ok:
            logger.warning(f"搜索频率限制触发: {client_id} - {rate_limit_msg}")
            raise HTTPException(status_code=429, detail=rate_limit_msg)
        
        # 使用安全验证器进行输入验证
        is_valid, error_msg = security_validator.validate_search_keyword(keyword)
        if not is_valid:
            logger.warning(f"搜索输入验证失败: {keyword} from IP: {client_ip} - {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
        
        # 清理输入
        keyword = security_validator.sanitize_input(keyword)
        
        logger.info(f"开始搜索: {keyword}")
        start_time = time.time()
        
        # 搜索内容
        try:
            search_results = search_content(keyword)
        except Exception as e:
            logger.error(f"WPS内容搜索失败: {e}")
            raise HTTPException(status_code=500, detail="内容搜索服务暂时不可用")
        
        # 处理搜索结果 - 使用优化的并行处理提高性能
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import asyncio
        
        processed_results = []
        
        def process_single_result(result):
            """处理单个搜索结果 - 优化版本"""
            processed_result = {
                "title": result.title,
                "links": [],
                "video_count_total": 0
            }
            
            # 收集所有需要验证的链接
            all_links = []
            
            # 添加百度网盘链接
            for baidu_link in result.baidu_links:
                all_links.append((baidu_link, "baidu", result.title))
            
            # 添加夸克网盘链接
            for quark_link in result.quark_links:
                all_links.append((quark_link, "quark", result.title))
            
            if not all_links:
                return None
            
            # 并行验证所有链接 - 增加线程池大小以提高并发性能
            valid_links = []
            max_workers = max(1, min(len(all_links), 8))  # 确保至少为1

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_link = {
                    executor.submit(validate_link, link_url, title): (link_url, link_type, title)
                    for link_url, link_type, title in all_links
                }
                
                for future in as_completed(future_to_link):
                    link_url, link_type, title = future_to_link[future]
                    try:
                        is_valid, error_msg = future.result(timeout=15)  # 添加超时控制
                        if is_valid:
                            valid_links.append((link_url, link_type))
                    except Exception as e:
                        logger.warning(f"验证链接失败: {link_url} - {e}")
            
            if not valid_links:
                return None
            
            # 分离夸克链接和百度链接，并行处理夸克文件信息获取
            quark_links = [(url, link_type) for url, link_type in valid_links if link_type == "quark"]
            baidu_links = [(url, link_type) for url, link_type in valid_links if link_type == "baidu"]
            
            # 处理百度网盘链接
            for link_url, link_type in baidu_links:
                processed_result["links"].append({
                    "url": link_url,
                    "type": "baidu",
                    "folder_name": "百度网盘",
                    "video_count": None
                })
            
            # 并行处理夸克网盘文件信息获取
            if quark_links:
                def get_quark_info(link_url):
                    """获取单个夸克链接的文件信息"""
                    try:
                        folder_name, update_time, file_count, video_count, folder_count, file_list = get_quark_folder_info(link_url)
                        
                        return {
                            "url": link_url,
                            "type": "quark",
                            "folder_name": folder_name or "夸克网盘",
                            "video_count": video_count,
                            "update_time": update_time,
                            "success": True
                        }
                    except Exception as e:
                        logger.warning(f"获取夸克文件信息失败: {link_url} - {e}")
                        return {
                            "url": link_url,
                            "type": "quark",
                            "folder_name": "夸克网盘",
                            "video_count": None,
                            "success": False
                        }
                
                # 并行获取所有夸克链接的文件信息
                quark_max_workers = max(1, min(len(quark_links), 5))  # 确保至少为1，限制夸克API并发数
                with ThreadPoolExecutor(max_workers=quark_max_workers) as quark_executor:
                    quark_futures = {
                        quark_executor.submit(get_quark_info, link_url): link_url
                        for link_url, _ in quark_links
                    }
                    
                    for future in as_completed(quark_futures):
                        try:
                            link_info = future.result(timeout=20)  # 夸克API超时时间稍长

                            # 新规则：只添加有视频文件的夸克链接
                            video_count = link_info.get("video_count")
                            if video_count is not None and video_count > 0:
                                processed_result["links"].append(link_info)
                                processed_result["video_count_total"] += video_count
                            else:
                                logger.info(f"过滤夸克链接(无视频文件): {link_info.get('url')} - 视频数量: {video_count}")

                        except Exception as e:
                            link_url = quark_futures[future]
                            logger.error(f"处理夸克链接超时: {link_url} - {e}")
                            # 超时的链接不再添加到结果中
            
            return processed_result if processed_result["links"] else None
        
        # 并行处理所有搜索结果 - 优化线程池配置
        max_result_workers = max(1, min(len(search_results), 4))  # 确保至少为1，适度增加并发数
        with ThreadPoolExecutor(max_workers=max_result_workers) as executor:
            future_to_result = {
                executor.submit(process_single_result, result): result
                for result in search_results
            }
            
            for future in as_completed(future_to_result):
                try:
                    processed_result = future.result(timeout=30)  # 添加总体超时控制
                    if processed_result:
                        processed_results.append(processed_result)
                except Exception as e:
                    logger.error(f"处理搜索结果失败: {e}")
        
        # 加密所有结果中的链接
        encrypted_results = []
        for result in processed_results:
            encrypted_result = link_encryptor.encrypt_links_in_result(result)
            encrypted_results.append(encrypted_result)

        # 按视频文件数量排序（从多到少）
        # 优先排序规则：
        # 1. 有视频文件数量的结果优先
        # 2. 视频文件数量多的优先
        # 3. 有链接但无视频数量信息的排在最后
        def sort_key(result):
            video_count = result["video_count_total"]
            has_video_count = any(link.get("video_count") is not None for link in result["links"])

            if has_video_count and video_count > 0:
                return (2, video_count)  # 有视频数量且大于0，优先级最高
            elif has_video_count and video_count == 0:
                return (1, 0)  # 有视频数量但为0，优先级中等
            else:
                return (0, 0)  # 无视频数量信息，优先级最低

        encrypted_results.sort(key=sort_key, reverse=True)

        search_time = time.time() - start_time
        logger.info(f"搜索完成: {keyword}, 找到 {len(encrypted_results)} 个有效结果, 耗时 {search_time:.2f} 秒")

        # 统计结果信息
        total_video_count = sum(result["video_count_total"] for result in encrypted_results)
        results_with_video_count = sum(1 for result in encrypted_results if result["video_count_total"] > 0)

        return {
            "success": True,
            "data": encrypted_results,
            "total": len(encrypted_results),
            "search_time": round(search_time, 2),
            "keyword": keyword,
            "message": f"搜索完成，找到 {len(encrypted_results)} 个结果",
            "statistics": {
                "total_video_count": total_video_count,
                "results_with_video_count": results_with_video_count,
                "sorted_by": "video_count_desc"
            },
            "security": {
                "links_encrypted": link_encryptor.enable_encryption,
                "authenticated_user": getattr(http_request.state, 'username', 'Unknown')
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索API异常: {e}")
        raise HTTPException(status_code=500, detail="搜索服务暂时不可用，请稍后重试")

@app.post("/api/auth/search")
async def auth_search_movies(request: SearchRequest, http_request: Request):
    """认证搜索电影接口（需要API密钥认证，返回加密链接）"""
    try:
        # 获取客户端标识符
        client_id = get_client_identifier(http_request)
        client_ip = http_request.client.host

        keyword = request.keyword.strip()

        # 使用严格的频率限制检查（1次/分钟）
        rate_limit_ok, rate_limit_msg = check_rate_limit(client_id, "search")
        if not rate_limit_ok:
            logger.warning(f"认证搜索频率限制触发: {client_id} - {rate_limit_msg}")
            raise HTTPException(status_code=429, detail=rate_limit_msg)

        # 使用安全验证器进行输入验证
        is_valid, error_msg = security_validator.validate_search_keyword(keyword)
        if not is_valid:
            logger.warning(f"认证搜索输入验证失败: {keyword} from IP: {client_ip} - {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)

        # 清理输入
        keyword = security_validator.sanitize_input(keyword)

        logger.info(f"开始认证搜索: {keyword}")
        start_time = time.time()

        # 搜索内容（复用相同的搜索逻辑）
        try:
            search_results = search_content(keyword)
        except Exception as e:
            logger.error(f"WPS内容搜索失败: {e}")
            raise HTTPException(status_code=500, detail="内容搜索服务暂时不可用")

        # 处理搜索结果（复用相同的处理逻辑）
        from concurrent.futures import ThreadPoolExecutor, as_completed

        processed_results = []

        def process_single_result(result):
            """处理单个搜索结果"""
            processed_result = {
                "title": result.title,
                "links": [],
                "video_count_total": 0
            }

            # 收集所有需要验证的链接
            all_links = []

            # 添加百度网盘链接
            for baidu_link in result.baidu_links:
                all_links.append((baidu_link, "baidu", result.title))

            # 添加夸克网盘链接
            for quark_link in result.quark_links:
                all_links.append((quark_link, "quark", result.title))

            if not all_links:
                return None

            # 并行验证所有链接
            valid_links = []
            max_workers = max(1, min(len(all_links), 8))

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_link = {
                    executor.submit(validate_link, link_url, title): (link_url, link_type, title)
                    for link_url, link_type, title in all_links
                }

                for future in as_completed(future_to_link):
                    link_url, link_type, title = future_to_link[future]
                    try:
                        is_valid, error_msg = future.result(timeout=15)
                        if is_valid:
                            valid_links.append((link_url, link_type))
                    except Exception as e:
                        logger.warning(f"验证链接失败: {link_url} - {e}")

            if not valid_links:
                return None

            # 分离夸克链接和百度链接
            quark_links = [(url, link_type) for url, link_type in valid_links if link_type == "quark"]
            baidu_links = [(url, link_type) for url, link_type in valid_links if link_type == "baidu"]

            # 处理百度网盘链接
            for link_url, link_type in baidu_links:
                processed_result["links"].append({
                    "url": link_url,
                    "type": "baidu",
                    "folder_name": "百度网盘",
                    "video_count": None
                })

            # 并行处理夸克网盘文件信息获取
            if quark_links:
                def get_quark_info(link_url):
                    try:
                        folder_name, update_time, file_count, video_count, folder_count, file_list = get_quark_folder_info(link_url)

                        return {
                            "url": link_url,
                            "type": "quark",
                            "folder_name": folder_name or "夸克网盘",
                            "video_count": video_count,
                            "update_time": update_time,
                            "success": True
                        }
                    except Exception as e:
                        logger.warning(f"获取夸克文件信息失败: {link_url} - {e}")
                        return {
                            "url": link_url,
                            "type": "quark",
                            "folder_name": "夸克网盘",
                            "video_count": None,
                            "success": False
                        }

                quark_max_workers = max(1, min(len(quark_links), 5))
                with ThreadPoolExecutor(max_workers=quark_max_workers) as quark_executor:
                    quark_futures = {
                        quark_executor.submit(get_quark_info, link_url): link_url
                        for link_url, _ in quark_links
                    }

                    for future in as_completed(quark_futures):
                        try:
                            link_info = future.result(timeout=20)

                            video_count = link_info.get("video_count")
                            if video_count is not None and video_count > 0:
                                processed_result["links"].append(link_info)
                                processed_result["video_count_total"] += video_count
                            else:
                                logger.info(f"过滤夸克链接(无视频文件): {link_info.get('url')} - 视频数量: {video_count}")

                        except Exception as e:
                            link_url = quark_futures[future]
                            logger.error(f"处理夸克链接超时: {link_url} - {e}")

            return processed_result if processed_result["links"] else None

        # 并行处理所有搜索结果
        max_result_workers = max(1, min(len(search_results), 4))
        with ThreadPoolExecutor(max_workers=max_result_workers) as executor:
            future_to_result = {
                executor.submit(process_single_result, result): result
                for result in search_results
            }

            for future in as_completed(future_to_result):
                try:
                    processed_result = future.result(timeout=30)
                    if processed_result:
                        processed_results.append(processed_result)
                except Exception as e:
                    logger.error(f"处理搜索结果失败: {e}")

        # 加密所有结果中的链接
        encrypted_results = []
        for result in processed_results:
            encrypted_result = link_encryptor.encrypt_links_in_result(result)
            encrypted_results.append(encrypted_result)

        # 按视频文件数量排序
        def sort_key(result):
            video_count = result["video_count_total"]
            has_video_count = any(link.get("video_count") is not None for link in result["links"])

            if has_video_count and video_count > 0:
                return (2, video_count)
            elif has_video_count and video_count == 0:
                return (1, 0)
            else:
                return (0, 0)

        encrypted_results.sort(key=sort_key, reverse=True)

        search_time = time.time() - start_time
        logger.info(f"认证搜索完成: {keyword}, 找到 {len(encrypted_results)} 个有效结果, 耗时 {search_time:.2f} 秒")

        # 统计结果信息
        total_video_count = sum(result["video_count_total"] for result in encrypted_results)
        results_with_video_count = sum(1 for result in encrypted_results if result["video_count_total"] > 0)

        return {
            "success": True,
            "data": encrypted_results,
            "total": len(encrypted_results),
            "search_time": round(search_time, 2),
            "keyword": keyword,
            "message": f"认证搜索完成，找到 {len(encrypted_results)} 个结果",
            "statistics": {
                "total_video_count": total_video_count,
                "results_with_video_count": results_with_video_count,
                "sorted_by": "video_count_desc"
            },
            "security": {
                "links_encrypted": link_encryptor.enable_encryption,
                "authenticated_user": getattr(http_request.state, 'username', 'Unknown')
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"认证搜索API异常: {e}")
        raise HTTPException(status_code=500, detail="认证搜索服务暂时不可用，请稍后重试")

@app.post("/api/search-cloudsave")
async def search_cloudsave_api(request: SearchRequest, http_request: Request):
    """CloudSave搜索接口（需要会话令牌认证，返回加密链接）"""
    try:
        # 获取客户端标识符
        client_id = get_client_identifier(http_request)
        client_ip = http_request.client.host

        keyword = request.keyword.strip()

        # 使用严格的频率限制检查（3次/分钟）
        rate_limit_ok, rate_limit_msg = check_rate_limit(client_id, "search-cloudsave")
        if not rate_limit_ok:
            logger.warning(f"CloudSave搜索频率限制触发: {client_id} - {rate_limit_msg}")
            raise HTTPException(status_code=429, detail=rate_limit_msg)

        # 使用安全验证器进行输入验证
        is_valid, error_msg = security_validator.validate_search_keyword(keyword)
        if not is_valid:
            logger.warning(f"搜索输入验证失败: {keyword} from IP: {client_ip} - {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)

        # 清理输入
        keyword = security_validator.sanitize_input(keyword)

        logger.info(f"开始CloudSave搜索: {keyword}")
        start_time = time.time()

        # CloudSave搜索
        try:
            search_results = search_cloudsave(keyword)
        except Exception as e:
            logger.error(f"CloudSave搜索失败: {e}")
            raise HTTPException(status_code=500, detail="CloudSave搜索服务暂时不可用，请稍后重试")

        # 加密CloudSave结果中的链接
        encrypted_cloudsave_results = {}
        for cloud_type, items in search_results.items():
            encrypted_items = []
            for item in items:
                # 加密每个项目中的链接
                encrypted_item = link_encryptor.encrypt_links_in_result(item)
                encrypted_items.append(encrypted_item)
            encrypted_cloudsave_results[cloud_type] = encrypted_items

        # 统计结果
        total_results = sum(len(items) for items in encrypted_cloudsave_results.values())

        end_time = time.time()
        search_time = end_time - start_time

        logger.info(f"CloudSave搜索完成: {keyword}, 找到 {len(encrypted_cloudsave_results)} 种云盘类型, 共 {total_results} 个资源, 耗时 {search_time:.2f} 秒")

        return {
            "success": True,
            "data": encrypted_cloudsave_results,
            "keyword": keyword,
            "total_cloud_types": len(encrypted_cloudsave_results),
            "total_results": total_results,
            "search_time": round(search_time, 2),
            "timestamp": datetime.now().isoformat(),
            "security": {
                "links_encrypted": link_encryptor.enable_encryption,
                "authenticated_user": getattr(http_request.state, 'username', 'Session User')
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CloudSave搜索API异常: {e}")
        raise HTTPException(status_code=500, detail="CloudSave搜索服务暂时不可用，请稍后重试")

@app.post("/api/decrypt-link")
async def decrypt_link_api(request: DecryptLinkRequest, http_request: Request):
    """链接解密接口"""
    try:
        # 获取客户端标识符
        client_id = get_client_identifier(http_request)

        # 频率限制检查
        rate_limit_ok, rate_limit_msg = check_rate_limit(client_id, "default")
        if not rate_limit_ok:
            logger.warning(f"解密链接频率限制触发: {client_id} - {rate_limit_msg}")
            raise HTTPException(status_code=429, detail=rate_limit_msg)

        encrypted_link = request.encrypted_link.strip()

        # 验证输入
        if not encrypted_link:
            raise HTTPException(status_code=400, detail="加密链接不能为空")

        # 解密链接
        try:
            decrypted_link = link_encryptor.decrypt_link(encrypted_link)

            # 记录解密操作（不记录实际链接内容）
            username = getattr(http_request.state, 'username', 'Unknown')
            logger.info(f"链接解密请求: 用户 {username} from IP: {http_request.client.host}")

            return {
                "success": True,
                "decrypted_link": decrypted_link,
                "message": "链接解密成功",
                "timestamp": datetime.now().isoformat(),
                "security": {
                    "authenticated_user": username
                }
            }

        except Exception as e:
            logger.error(f"链接解密失败: {e}")
            raise HTTPException(status_code=400, detail="链接解密失败，请检查链接格式")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解密链接API异常: {e}")
        raise HTTPException(status_code=500, detail="解密服务暂时不可用，请稍后重试")

def update_hot_movies_background():
    """后台更新热门电影数据"""
    global hot_movies, last_update_time
    
    while True:
        try:
            logger.info("后台定时更新热门电影数据...")
            new_movies = get_maoyan_hot_shows()
            if new_movies and len(new_movies) >= 10:  # 确保获取到足够的数据
                hot_movies = new_movies
                last_update_time = time.time()
                logger.info(f"后台热门电影数据更新成功，共{len(hot_movies)}部电影")
            else:
                logger.warning("后台更新热门电影数据失败或数据不足，保持现有数据")
            
            # 等待更新间隔
            time.sleep(update_interval)
        except Exception as e:
            logger.error(f"后台更新热门电影数据异常: {e}")
            time.sleep(3600)  # 出错后等待1小时再试

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global hot_movies, last_update_time, startup_time
    
    startup_time = time.time()
    logger.info("=" * 50)
    logger.info("移动端电影搜索应用启动中...")
    logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    # 初始获取一次热门电影数据
    try:
        logger.info("初始化热门电影数据...")
        movies = get_maoyan_hot_shows()
        if movies:
            hot_movies = movies
            last_update_time = time.time()
            logger.info(f"✓ 初始热门电影数据获取成功，共{len(hot_movies)}部电影")
        else:
            hot_movies = get_default_hot_movies()
            last_update_time = time.time()
            logger.info("⚠ 使用默认热门电影数据")
    except Exception as e:
        logger.error(f"✗ 初始化热门电影数据失败: {e}")
        hot_movies = get_default_hot_movies()
        last_update_time = time.time()
        logger.info("⚠ 使用默认热门电影数据作为备选")
    
    # 启动后台更新线程
    try:
        update_thread = threading.Thread(target=update_hot_movies_background, daemon=True)
        update_thread.start()
        logger.info("✓ 后台更新线程已启动")
    except Exception as e:
        logger.error(f"✗ 启动后台更新线程失败: {e}")
    
    logger.info("=" * 50)
    logger.info("应用启动完成！")
    logger.info("健康检查: GET /health")
    logger.info("API文档: GET /docs")
    logger.info("=" * 50)

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("=" * 50)
    logger.info("应用正在关闭...")
    logger.info(f"运行时长: {time.time() - startup_time:.2f} 秒")
    logger.info("感谢使用移动端电影搜索应用！")
    logger.info("=" * 50)

if __name__ == "__main__":
    logger.info("启动移动端电影搜索应用...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        reload=False,  # 关闭热重载，减少日志输出
        log_level="warning",  # 只显示警告和错误，减少INFO日志
        # 如果需要热重载，可以使用以下配置：
        # reload=True,
        # reload_excludes=["logs/*", "*.log", "__pycache__/*", "*.pyc"]
    )