#!/bin/bash

# Nginx配置脚本
# 配置反向代理和SSL

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DOMAIN=${1:-"yourdomain.com"}
APP_PORT=${2:-"8506"}
NGINX_CONF_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
CONF_NAME="movie-search"

# 创建Nginx配置文件
create_nginx_config() {
    log_info "创建Nginx配置文件..."
    
    sudo tee "$NGINX_CONF_DIR/$CONF_NAME" > /dev/null <<EOF
# 移动端电影搜索应用 - Nginx配置

# 限制请求频率
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/m;
limit_req_zone \$binary_remote_addr zone=search:10m rate=5r/m;

# HTTP重定向到HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Let's Encrypt验证
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # 重定向到HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS主配置
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
    
    # 基础配置
    client_max_body_size 1M;
    client_body_timeout 10s;
    client_header_timeout 10s;
    keepalive_timeout 65s;
    send_timeout 10s;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # 静态文件缓存
    location /static/ {
        alias /opt/movie-search/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # 安全配置
        location ~* \.(js|css)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API接口特殊限制
    location /api/search {
        limit_req zone=search burst=3 nodelay;
        limit_req_status 429;
        
        proxy_pass http://127.0.0.1:$APP_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$server_name;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查（不记录日志）
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:$APP_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 监控接口（限制访问）
    location /api/monitoring {
        # 可以添加IP白名单
        # allow ***********/24;
        # deny all;
        
        limit_req zone=api burst=5 nodelay;
        
        proxy_pass http://127.0.0.1:$APP_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 主要应用代理
    location / {
        limit_req zone=api burst=10 nodelay;
        
        proxy_pass http://127.0.0.1:$APP_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$server_name;
        
        # WebSocket支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/html;
        internal;
    }
    
    location = /50x.html {
        root /var/www/html;
        internal;
    }
    
    # 访问日志
    access_log /var/log/nginx/movie-search.access.log;
    error_log /var/log/nginx/movie-search.error.log;
}
EOF
    
    log_success "Nginx配置文件创建完成"
}

# 启用站点配置
enable_site() {
    log_info "启用站点配置..."
    
    # 创建软链接
    sudo ln -sf "$NGINX_CONF_DIR/$CONF_NAME" "$NGINX_ENABLED_DIR/$CONF_NAME"
    
    # 删除默认站点（如果存在）
    if [ -f "$NGINX_ENABLED_DIR/default" ]; then
        sudo rm "$NGINX_ENABLED_DIR/default"
        log_info "已删除默认站点配置"
    fi
    
    log_success "站点配置已启用"
}

# 测试Nginx配置
test_nginx_config() {
    log_info "测试Nginx配置..."
    
    if sudo nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 重启Nginx
restart_nginx() {
    log_info "重启Nginx..."
    
    sudo systemctl restart nginx
    
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx重启成功"
    else
        log_error "Nginx重启失败"
        sudo systemctl status nginx --no-pager -l
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 启用UFW
    sudo ufw --force enable
    
    # 允许SSH
    sudo ufw allow ssh
    
    # 允许HTTP和HTTPS
    sudo ufw allow 'Nginx Full'
    
    # 显示防火墙状态
    sudo ufw status
    
    log_success "防火墙配置完成"
}

# 主函数
main() {
    if [ -z "$1" ]; then
        log_error "请提供域名参数"
        log_info "使用方法: ./scripts/setup_nginx.sh yourdomain.com [port]"
        exit 1
    fi
    
    log_info "开始配置Nginx，域名: $DOMAIN，端口: $APP_PORT"
    
    create_nginx_config
    enable_site
    test_nginx_config
    restart_nginx
    configure_firewall
    
    log_success "Nginx配置完成！"
    log_warning "注意: SSL证书配置需要单独运行:"
    log_info "sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN"
}

# 错误处理
trap 'log_error "Nginx配置过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
