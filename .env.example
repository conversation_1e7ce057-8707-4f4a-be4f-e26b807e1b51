# 环境配置示例文件
# 复制此文件为 .env 并修改相应的值

# 基础配置
ENVIRONMENT=production
LOG_LEVEL=INFO
LOG_DIR=logs

# 安全配置
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# API密钥配置（格式：key1:name1,key2:name2）
# 请生成强密钥并妥善保管
API_KEYS=demo_key_123456:Demo User,prod_key_abcdef:Production User
ENABLE_API_KEY_AUTH=true

# 请求签名验证配置
ENABLE_REQUEST_SIGNATURE=true
SIGNATURE_SECRET=your-secret-key-change-in-production-32chars
SIGNATURE_TIMEOUT=300

# 链接加密配置
ENABLE_LINK_ENCRYPTION=true
LINK_ENCRYPTION_KEY=your-encryption-key-32-chars-long

# 频率限制配置
SEARCH_RATE_LIMIT=3
HOT_MOVIES_RATE_LIMIT=10
DEFAULT_RATE_LIMIT=20
MAX_VIOLATIONS=5
BLOCK_DURATION=120

# 输入验证配置
MAX_KEYWORD_LENGTH=50
MIN_KEYWORD_LENGTH=2
MAX_CHAR_REPETITION=10

# 缓存配置
CACHE_EXPIRE_TIME=300
MAX_CACHE_SIZE=100

# 性能监控配置
ENABLE_PERFORMANCE_MONITORING=true
SLOW_REQUEST_THRESHOLD=5.0

# 日志脱敏配置
ENABLE_LOG_SANITIZATION=true

# 用户代理过滤
ENABLE_USER_AGENT_FILTERING=true

# 地理位置限制（可选）
ENABLE_GEO_BLOCKING=false
BLOCKED_COUNTRIES=

# 可信代理配置
TRUSTED_PROXIES=

# 蜜罐配置
ENABLE_HONEYPOT=false
