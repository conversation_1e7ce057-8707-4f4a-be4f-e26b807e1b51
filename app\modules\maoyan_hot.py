"""
猫眼热门电影数据获取模块
"""

import requests
import logging
from bs4 import BeautifulSoup
from typing import List

logger = logging.getLogger(__name__)

def get_maoyan_hot_shows() -> List[str]:
    """获取猫眼热门电影"""
    logger.info("开始获取猫眼热门电影...")
    
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
    }

    try:
        logger.info("正在请求猫眼热门...")
        response = requests.get('https://piaofang.maoyan.com/web-heat', headers=headers, timeout=15)
        logger.info(f"猫眼热门请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            movies = soup.select('tbody.table-body tr.body-row')
            logger.info(f"解析到电影数量: {len(movies)}")
            
            # 如果无法通过正常方式获取，尝试使用备选方法
            if not movies:
                logger.info("尝试备选解析方法...")
                movies = soup.select('tr.body-row')
                logger.info(f"备选方法解析到电影数量: {len(movies)}")
                
                # 如果备选方法仍无结果，返回固定的热门电影列表
                if not movies:
                    logger.info("无法解析猫眼网页，返回默认热门电影列表")
                    return get_default_hot_movies()
            
            # 提取电影名称，处理可能的错误情况
            result = []
            for movie in movies[:15]:
                try:
                    name_elem = movie.select_one('p.video-name')
                    if name_elem:
                        result.append(name_elem.text.strip())
                    else:
                        # 尝试查找其他可能包含电影名的元素
                        name_elem = movie.select_one('td') or movie.select_one('div')
                        if name_elem:
                            result.append(name_elem.text.strip())
                except Exception as e:
                    logger.error(f"解析单个电影名称时出错: {e}")
            
            logger.info(f"成功提取 {len(result)} 部电影名称")
            return result if result else get_default_hot_movies()
        else:
            logger.error(f"获取猫眼热门失败, 状态码: {response.status_code}")
            return get_default_hot_movies()
            
    except Exception as e:
        logger.error(f"获取猫眼热门失败(异常): {e}")
        return get_default_hot_movies()

def get_default_hot_movies() -> List[str]:
    """获取默认热门电影列表"""
    return [
        "流浪地球3", "热辣滚烫", "独行月球2", "熊出没", "消失的她", 
        "长津湖之水门桥", "奇迹·笨小孩", "这个杀手不太冷静", "狙击手", "四海",
        "喜羊羊与灰太狼之筐出未来", "梅艳芳", "反贪风暴5：最终章", "李茂扮太子", "爱情神话"
    ]