version: '3.8'

services:
  movie-search-app:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: movie-search-app
    ports:
      - "8506:8506"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs:rw
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8506/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    networks:
      - movie-search-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  movie-search-network:
    driver: bridge