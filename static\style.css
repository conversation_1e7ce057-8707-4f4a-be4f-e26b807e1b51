/* 现代化移动端电影搜索应用样式 - 基于参考项目设计 */
/* 基础重置和变量 */
:root {
    --primary: #3B82F6;
    --primary-hover: #2563EB;
    --secondary: #10B981;
    --secondary-hover: #059669;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    --green-100: #DCFCE7;
    --green-600: #16A34A;
    --green-700: #15803D;
    --green-800: #166534;
    --red-50: #FEF2F2;
    --red-100: #FEE2E2;
    --red-200: #FECACA;
    --red-800: #991B1B;
    --yellow-100: #FEF3C7;
    --yellow-800: #92400E;
    --blue-500: #3B82F6;
    --blue-600: #2563EB;
    --blue-700: #1D4ED8;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* 工具类 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.hidden {
    display: none !important;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-grow {
    flex-grow: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.flex-wrap {
    flex-wrap: wrap;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.space-x-2 > * + * {
    margin-left: 0.5rem;
}

.space-x-3 > * + * {
    margin-left: 0.75rem;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-4 {
    gap: 1rem;
}

/* 背景和文本颜色 */
.bg-gray-50 {
    background-color: var(--gray-50);
}

.bg-gray-100 {
    background-color: var(--gray-100);
}

.bg-gray-800 {
    background-color: var(--gray-800);
}

.bg-white {
    background-color: white;
}

.bg-primary {
    background-color: #3B82F6; /* 备用颜色 */
    background-color: var(--primary);
}

.bg-green-50 {
    background-color: var(--green-100);
}

.bg-red-50 {
    background-color: var(--red-100);
}

.bg-yellow-50 {
    background-color: var(--yellow-100);
}

.bg-black {
    background-color: black;
}

.bg-opacity-50 {
    background-color: rgba(0, 0, 0, 0.5);
}

.text-white {
    color: white;
}

.text-gray-400 {
    color: var(--gray-400);
}

.text-gray-500 {
    color: var(--gray-500);
}

.text-gray-600 {
    color: var(--gray-600);
}

.text-gray-700 {
    color: var(--gray-700);
}

.text-gray-900 {
    color: var(--gray-900);
}

.text-green-800 {
    color: var(--green-800);
}

.text-red-800 {
    color: var(--red-800);
}

.text-yellow-800 {
    color: var(--yellow-800);
}

.text-green-400 {
    color: var(--green-400);
}

.text-red-400 {
    color: var(--red-400);
}

.text-primary {
    color: var(--primary);
}

.text-blue-700 {
    color: var(--blue-700);
}

/* 字体大小和粗细 */
.text-xs {
    font-size: 0.75rem;
}

.text-sm {
    font-size: 0.875rem;
}

/* 移动端字体大小优化 */
@media (max-width: 768px) {
    .text-sm {
        font-size: 1rem; /* 移动端增大小字体 */
    }

    .text-xs {
        font-size: 0.875rem; /* 移动端增大超小字体 */
    }

    /* 移动端关键样式强化 */
    .bg-primary {
        background-color: var(--primary) !important;
    }

    .bg-gray-50 {
        background-color: var(--gray-50) !important;
    }

    .card {
        background-color: white !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    .text-white {
        color: white !important;
    }
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.text-4xl {
    font-size: 2.25rem;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.font-normal {
    font-weight: 400;
}

/* 间距 */
.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-4 {
    padding: 1rem;
}

.p-6 {
    padding: 1.5rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.pl-3 {
    padding-left: 0.75rem;
}

.pr-2 {
    padding-right: 0.5rem;
}

.pr-3 {
    padding-right: 0.75rem;
}

.pr-8 {
    padding-right: 2rem;
}

.pr-12 {
    padding-right: 3rem;
}

.pt-3 {
    padding-top: 0.75rem;
}

.m-0 {
    margin: 0;
}

.ml-1 {
    margin-left: 0.25rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-3 {
    margin-left: 0.75rem;
}

.ml-4 {
    margin-left: 1rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-auto {
    margin-top: auto;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

/* 尺寸 */
.w-full {
    width: 100%;
}

.w-20 {
    width: 5rem;
}

.w-24 {
    width: 6rem;
}

.w-0 {
    width: 0;
}

.h-4 {
    height: 1rem;
}

.h-5 {
    height: 1.25rem;
}

.h-6 {
    height: 1.5rem;
}

.h-8 {
    height: 2rem;
}

.min-h-screen {
    min-height: 100vh;
}

.max-w-sm {
    max-width: 24rem;
}

.max-w-2xl {
    max-width: 42rem;
}

/* 定位 */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.inset-y-0 {
    top: 0;
    bottom: 0;
}

.right-0 {
    right: 0;
}

.right-4 {
    right: 1rem;
}

.bottom-4 {
    bottom: 1rem;
}

/* 边框和圆角 */
.border {
    border-width: 1px;
}

.border-t {
    border-top-width: 1px;
}

.border-gray-100 {
    border-color: var(--gray-100);
}

.border-gray-200 {
    border-color: var(--gray-200);
}

.border-gray-300 {
    border-color: var(--gray-300);
}

.border-red-200 {
    border-color: var(--red-100);
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-full {
    border-radius: 9999px;
}

/* 阴影 */
.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 透明度 */
.opacity-90 {
    opacity: 0.9;
}

/* Z-index */
.z-50 {
    z-index: 50;
}

/* 溢出 */
.overflow-hidden {
    overflow: hidden;
}

/* 网格 */
.grid {
    display: grid;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

/* 过渡效果 */
.transition-colors {
    transition-property: color, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.duration-200 {
    transition-duration: 200ms;
}

/* 组件样式 - 基于参考项目设计 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem; /* 更大的圆角 */
    font-weight: 500;
    transition: color 0.2s, background-color 0.2s, border-color 0.2s;
    border: none;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
    min-height: 44px;
    outline: none;
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 16px;
        min-height: 48px;
        border-radius: 0.75rem;
        font-weight: 500;
    }

    .btn-primary {
        background-color: var(--primary) !important;
        color: white !important;
    }

    .btn-primary:hover {
        background-color: var(--primary-hover) !important;
    }
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn-primary {
    background-color: #3B82F6; /* 备用颜色 */
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-600);
    border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
    background-color: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 搜索提示文字样式 */
.search-hint {
    font-size: 0.75rem;
    color: var(--gray-500);
    text-align: left;
    margin: 0.5rem 0;
    line-height: 1.2;
}

/* 搜索按钮容器样式 */
.search-buttons-container {
    display: flex;
    gap: 0.5rem;
    width: 100%;
}

/* 搜索按钮状态样式 */
.search-btn {
    flex: 1;
    transition: all 0.2s ease-in-out;
    background-color: var(--primary) !important;
    color: white !important;
    border: 1px solid var(--primary) !important;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    outline: none;
}

.search-btn:hover {
    background-color: var(--primary-hover) !important;
}

.search-btn.active {
    background-color: var(--primary) !important;
    color: white !important;
    border-color: var(--primary) !important;
}

.search-btn:not(.active) {
    background-color: var(--primary) !important;
    color: white !important;
    border-color: var(--primary) !important;
}

.search-btn:not(.active):hover {
    background-color: var(--primary-hover) !important;
    color: white !important;
}

/* CloudSave结果样式 */
.cloudsave-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.cloud-icon {
    font-size: 1.2rem;
}

.cloud-name {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.cloudsave-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    gap: 1rem;
}

.cloudsave-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--gray-600);
    flex: 1;
}

.cloudsave-stats span {
    display: flex;
    align-items: center;
}

.cloudsave-actions {
    display: flex;
    flex-shrink: 0;
}

/* 移动端搜索按钮优化 */
@media (max-width: 768px) {
    .search-btn {
        padding: 0.75rem 1rem;
        font-size: 16px;
        min-height: 48px;
        border-radius: 0.75rem;
    }

    .search-hint {
        font-size: 0.7rem;
        margin: 0.4rem 0;
    }
}

/* 移动端CloudSave样式优化 */
@media (max-width: 640px) {
    .cloudsave-stats {
        flex-direction: column;
        gap: 0.5rem;
    }

    .cloudsave-actions {
        justify-content: center;
    }
}

.form-input {
    width: 100%;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem; /* 更大的圆角 */
    border: 1px solid var(--gray-300);
    transition: border-color 0.2s, box-shadow 0.2s;
    font-size: 16px; /* 防止iOS缩放 */
    line-height: 1.5;
    min-height: 44px;
    outline: none;
}

.form-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 1px var(--primary);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .form-input {
        padding: 0.75rem 1rem;
        font-size: 16px;
        min-height: 48px;
        border-radius: 0.75rem;
    }
}

.card {
    background-color: white;
    border-radius: 0.75rem; /* 12px圆角 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 热门电影按钮 - 照抄参考项目设计 */
.hot-movie-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 9999px; /* 完全圆角 */
    border: 1px solid;
    transition: background-color 0.2s, color 0.2s;
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
}

/* 前3个热门电影 - 红色主题 */
.hot-movie-btn.hot {
    background-color: var(--red-50);
    color: var(--red-800);
    border-color: var(--red-200);
}

.hot-movie-btn.hot:hover {
    background-color: var(--red-100);
}

/* 其他热门电影 - 灰色主题 */
.hot-movie-btn.normal {
    background-color: var(--gray-50);
    color: var(--gray-800);
    border-color: var(--gray-200);
}

.hot-movie-btn.normal:hover {
    background-color: var(--gray-100);
}

/* 搜索结果卡片 */
.result-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.2s;
}

.result-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.result-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* 移动端结果统计优化 */
@media (max-width: 768px) {
    .result-stats {
        font-size: 0.9rem;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }
}

.video-count-display {
    text-align: right;
    color: #EF4444 !important;
    font-weight: 500;
}

.result-links {
    border-top: 1px solid var(--gray-100);
    padding-top: 0.75rem;
}

.link-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-50);
    gap: 1rem;
}

.link-item:last-child {
    border-bottom: none;
}

.link-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.link-info > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.link-type {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.25rem;
    margin-right: 0.5rem;
}

.link-type.baidu {
    background-color: var(--blue-100);
    color: var(--blue-700);
}

.link-type.quark {
    background-color: var(--green-100);
    color: var(--green-800);
}

.link-name {
    font-size: 0.875rem;
    color: var(--gray-700);
    font-family: monospace;
    word-break: break-all;
    line-height: 1.4;
    font-weight: 500;
    text-align: center;
    flex: 1;
}

.link-meta {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.125rem;
}

.link-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.link-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.25rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-block;
}

.link-btn.primary {
    background-color: var(--primary);
    color: white;
}

.link-btn.primary:hover {
    background-color: var(--primary-hover);
}

.link-btn.secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.link-btn.secondary:hover {
    background-color: var(--gray-200);
}

/* 加载动画 */
.spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 确保居中显示 */
.container {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
}

.max-w-2xl {
    max-width: 42rem;
    margin-left: auto;
    margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
        margin-left: auto;
        margin-right: auto;
    }
    
    .max-w-2xl {
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .result-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .video-count-display {
        text-align: right;
    }
    
    .link-item {
        /* 保持水平布局，因为"链接检测有效"文字很短 */
        gap: 0.5rem;
        padding: 0.5rem 0;
    }

    .link-actions {
        /* 移动端按钮保持原有样式 */
        flex-shrink: 0;
    }

    /* CloudSave 手机端布局优化 - 保持一行显示 */
    .cloudsave-info-row {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        flex-wrap: nowrap;
    }

    .cloudsave-stats {
        flex: 1;
        justify-content: flex-start;
        gap: 0.5rem;
        flex-wrap: nowrap;
        overflow: hidden;
    }

    .cloudsave-stats span {
        white-space: nowrap;
        font-size: 0.8rem;
    }

    .cloudsave-actions {
        flex-shrink: 0;
    }

    .cloudsave-actions .link-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* 辅助功能 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦点样式 */
.focus\:outline-none:focus {
    outline: none;
}

.focus\:ring-2:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.focus\:ring-primary:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.focus\:ring-blue-500:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.focus\:border-transparent:focus {
    border-color: transparent;
}

/* Hover 效果 */
.hover\:bg-red-100:hover {
    background-color: #FEE2E2;
}

.hover\:bg-gray-100:hover {
    background-color: var(--gray-100);
}

.hover\:bg-gray-200:hover {
    background-color: var(--gray-200);
}

.hover\:text-blue-200:hover {
    color: var(--blue-200);
}

.hover\:text-gray-500:hover {
    color: var(--gray-500);
}

.hover\:text-blue-700:hover {
    color: var(--blue-700);
}

/* 通知提示样式 */
#notification {
    min-width: 320px;
    max-width: 400px;
}

#notification.success {
    border-left-color: #10B981;
}

#notification.warning {
    border-left-color: #F59E0B;
}

#notification.error {
    border-left-color: #EF4444;
}

#notification.show {
    transform: translateX(0);
    opacity: 1;
}

#notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* 状态指示器 */
.status-valid {
    color: var(--green-400);
}

.status-invalid {
    color: var(--red-400);
}

.status-checking {
    color: var(--gray-400);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}