"""
监控和指标收集模块
"""

import time
import threading
import logging
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class RequestMetrics:
    """请求指标数据类"""
    timestamp: float
    method: str
    path: str
    status_code: int
    response_time: float
    client_id: str
    user_agent: str
    error_message: Optional[str] = None

@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    active_connections: int

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history_size: int = 10000):
        self.max_history_size = max_history_size
        
        # 请求指标
        self.request_history = deque(maxlen=max_history_size)
        self.request_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.response_times = deque(maxlen=1000)
        
        # 系统指标
        self.system_history = deque(maxlen=max_history_size)
        
        # 安全指标
        self.attack_counts = defaultdict(int)
        self.blocked_ips = set()
        
        # 统计数据
        self.stats = {
            'total_requests': 0,
            'total_errors': 0,
            'total_attacks': 0,
            'uptime_start': time.time(),
            'last_reset': time.time()
        }
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 启动后台监控线程
        self.monitoring_thread = threading.Thread(target=self._background_monitoring, daemon=True)
        self.monitoring_thread.start()
    
    def record_request(self, method: str, path: str, status_code: int, 
                      response_time: float, client_id: str, user_agent: str = "",
                      error_message: Optional[str] = None):
        """记录请求指标"""
        with self.lock:
            metrics = RequestMetrics(
                timestamp=time.time(),
                method=method,
                path=path,
                status_code=status_code,
                response_time=response_time,
                client_id=client_id,
                user_agent=user_agent,
                error_message=error_message
            )
            
            self.request_history.append(metrics)
            self.response_times.append(response_time)
            
            # 更新计数器
            self.stats['total_requests'] += 1
            self.request_counts[f"{method} {path}"] += 1
            
            if status_code >= 400:
                self.stats['total_errors'] += 1
                self.error_counts[status_code] += 1
    
    def record_security_event(self, event_type: str, client_id: str, blocked: bool = False):
        """记录安全事件"""
        with self.lock:
            self.stats['total_attacks'] += 1
            self.attack_counts[event_type] += 1
            
            if blocked:
                self.blocked_ips.add(client_id)
    
    def record_system_metrics(self):
        """记录系统指标"""
        try:
            # 简化的系统指标收集
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics = SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                disk_usage_percent=disk.percent,
                active_connections=0  # 简化版本
            )
            
            with self.lock:
                self.system_history.append(metrics)
                
        except Exception as e:
            logger.error(f"记录系统指标失败: {e}")
    
    def get_request_stats(self, time_window: int = 3600) -> Dict[str, Any]:
        """获取请求统计信息"""
        with self.lock:
            now = time.time()
            cutoff_time = now - time_window
            
            recent_requests = [r for r in self.request_history if r.timestamp > cutoff_time]
            
            if not recent_requests:
                return {
                    'total_requests': 0,
                    'error_rate': 0,
                    'avg_response_time': 0,
                    'requests_per_minute': 0,
                    'status_codes': {},
                    'top_endpoints': []
                }
            
            total_requests = len(recent_requests)
            error_requests = len([r for r in recent_requests if r.status_code >= 400])
            error_rate = (error_requests / total_requests) * 100 if total_requests > 0 else 0
            
            response_times = [r.response_time for r in recent_requests]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            requests_per_minute = (total_requests / time_window) * 60
            
            # 状态码分布
            status_codes = defaultdict(int)
            for request in recent_requests:
                status_codes[request.status_code] += 1
            
            # 热门端点
            endpoint_counts = defaultdict(int)
            for request in recent_requests:
                endpoint_counts[f"{request.method} {request.path}"] += 1
            
            top_endpoints = sorted(endpoint_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                'total_requests': total_requests,
                'error_rate': round(error_rate, 2),
                'avg_response_time': round(avg_response_time, 3),
                'requests_per_minute': round(requests_per_minute, 2),
                'status_codes': dict(status_codes),
                'top_endpoints': top_endpoints
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        request_stats = self.get_request_stats(300)  # 最近5分钟
        
        health_issues = []
        
        # 检查错误率
        if request_stats['error_rate'] > 10:
            health_issues.append(f"高错误率: {request_stats['error_rate']}%")
        
        # 检查响应时间
        if request_stats['avg_response_time'] > 5:
            health_issues.append(f"响应时间过长: {request_stats['avg_response_time']}s")
        
        # 确定健康状态
        if not health_issues:
            status = "healthy"
        elif len(health_issues) <= 2:
            status = "degraded"
        else:
            status = "unhealthy"
        
        uptime = time.time() - self.stats['uptime_start']
        
        return {
            'status': status,
            'uptime_seconds': int(uptime),
            'issues': health_issues,
            'last_check': datetime.now().isoformat(),
            'stats_summary': {
                'total_requests': self.stats['total_requests'],
                'total_errors': self.stats['total_errors'],
                'error_rate': request_stats['error_rate'],
                'avg_response_time': request_stats['avg_response_time']
            }
        }
    
    def _background_monitoring(self):
        """后台监控线程"""
        while True:
            try:
                # 每30秒记录一次系统指标
                self.record_system_metrics()
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"后台监控异常: {e}")
                time.sleep(60)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        with self.lock:
            if not self.system_history:
                return {}
            
            latest_metrics = self.system_history[-1]
            return {
                'cpu_percent': latest_metrics.cpu_percent,
                'memory_percent': latest_metrics.memory_percent,
                'memory_used_mb': latest_metrics.memory_used_mb,
                'disk_usage_percent': latest_metrics.disk_usage_percent,
                'active_connections': latest_metrics.active_connections,
                'timestamp': latest_metrics.timestamp
            }
    
    def reset_stats(self):
        """重置统计数据"""
        with self.lock:
            self.request_history.clear()
            self.request_counts.clear()
            self.error_counts.clear()
            self.response_times.clear()
            self.system_history.clear()
            self.attack_counts.clear()
            self.blocked_ips.clear()
            
            self.stats = {
                'total_requests': 0,
                'total_errors': 0,
                'total_attacks': 0,
                'uptime_start': time.time(),
                'last_reset': time.time()
            }

# 创建全局指标收集器实例
metrics_collector = MetricsCollector()