#!/bin/bash

# SSL证书配置脚本
# 使用Let's Encrypt自动配置SSL证书

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DOMAIN=${1:-"yourdomain.com"}
EMAIL=${2:-"admin@$DOMAIN"}

# 检查域名解析
check_domain_resolution() {
    log_info "检查域名解析..."
    
    # 获取服务器公网IP
    SERVER_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || curl -s icanhazip.com)
    
    if [ -z "$SERVER_IP" ]; then
        log_error "无法获取服务器公网IP"
        exit 1
    fi
    
    log_info "服务器IP: $SERVER_IP"
    
    # 检查主域名解析
    DOMAIN_IP=$(dig +short "$DOMAIN" | tail -n1)
    if [ "$DOMAIN_IP" != "$SERVER_IP" ]; then
        log_warning "域名 $DOMAIN 解析IP ($DOMAIN_IP) 与服务器IP ($SERVER_IP) 不匹配"
        log_warning "请确保域名已正确解析到服务器IP"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "域名解析检查通过"
    fi
}

# 安装Certbot
install_certbot() {
    log_info "检查Certbot安装..."
    
    if ! command -v certbot &> /dev/null; then
        log_info "安装Certbot..."
        sudo apt-get update
        sudo apt-get install -y certbot python3-certbot-nginx
    else
        log_info "Certbot已安装"
    fi
}

# 获取SSL证书
obtain_ssl_certificate() {
    log_info "获取SSL证书..."
    
    # 停止Nginx以释放80端口
    sudo systemctl stop nginx
    
    # 使用standalone模式获取证书
    sudo certbot certonly \
        --standalone \
        --non-interactive \
        --agree-tos \
        --email "$EMAIL" \
        --domains "$DOMAIN,www.$DOMAIN" \
        --rsa-key-size 4096
    
    # 重启Nginx
    sudo systemctl start nginx
    
    log_success "SSL证书获取成功"
}

# 配置自动续期
setup_auto_renewal() {
    log_info "配置SSL证书自动续期..."
    
    # 创建续期脚本
    sudo tee "/etc/cron.d/certbot-renewal" > /dev/null <<EOF
# SSL证书自动续期
# 每天凌晨2点检查证书是否需要续期
0 2 * * * root certbot renew --quiet --nginx --post-hook "systemctl reload nginx"
EOF
    
    # 测试续期
    sudo certbot renew --dry-run
    
    log_success "SSL证书自动续期配置完成"
}

# 更新Nginx配置以使用SSL
update_nginx_ssl_config() {
    log_info "更新Nginx SSL配置..."
    
    # 检查证书文件是否存在
    if [ ! -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        log_error "SSL证书文件不存在"
        exit 1
    fi
    
    # 测试Nginx配置
    if sudo nginx -t; then
        log_success "Nginx配置有效"
        sudo systemctl reload nginx
    else
        log_error "Nginx配置无效"
        exit 1
    fi
}

# 配置SSL安全等级
configure_ssl_security() {
    log_info "配置SSL安全参数..."
    
    # 生成DH参数（如果不存在）
    if [ ! -f "/etc/ssl/certs/dhparam.pem" ]; then
        log_info "生成DH参数（这可能需要几分钟）..."
        sudo openssl dhparam -out /etc/ssl/certs/dhparam.pem 2048
    fi
    
    # 创建SSL配置片段
    sudo tee "/etc/nginx/snippets/ssl-params.conf" > /dev/null <<EOF
# SSL配置参数
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_dhparam /etc/ssl/certs/dhparam.pem;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
ssl_ecdh_curve secp384r1;
ssl_session_timeout 10m;
ssl_session_cache shared:SSL:10m;
ssl_session_tickets off;
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# 安全头
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
EOF
    
    log_success "SSL安全参数配置完成"
}

# 测试SSL配置
test_ssl_configuration() {
    log_info "测试SSL配置..."
    
    # 等待服务启动
    sleep 5
    
    # 测试HTTPS连接
    if curl -s -I "https://$DOMAIN" | grep -q "HTTP/"; then
        log_success "HTTPS连接测试成功"
    else
        log_warning "HTTPS连接测试失败，请检查配置"
    fi
    
    # 测试HTTP重定向
    if curl -s -I "http://$DOMAIN" | grep -q "301"; then
        log_success "HTTP重定向测试成功"
    else
        log_warning "HTTP重定向测试失败"
    fi
}

# 显示SSL信息
show_ssl_info() {
    log_info "SSL证书信息："
    sudo certbot certificates
    
    log_info "SSL配置完成！"
    log_info "访问地址："
    log_info "  HTTPS: https://$DOMAIN"
    log_info "  HTTPS: https://www.$DOMAIN"
    log_info ""
    log_info "SSL测试工具："
    log_info "  SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
    log_info "  Mozilla Observatory: https://observatory.mozilla.org/analyze/$DOMAIN"
}

# 主函数
main() {
    if [ -z "$1" ]; then
        log_error "请提供域名参数"
        log_info "使用方法: ./scripts/setup_ssl.sh yourdomain.com [<EMAIL>]"
        exit 1
    fi
    
    log_info "开始配置SSL证书，域名: $DOMAIN，邮箱: $EMAIL"
    
    check_domain_resolution
    install_certbot
    obtain_ssl_certificate
    setup_auto_renewal
    update_nginx_ssl_config
    configure_ssl_security
    test_ssl_configuration
    show_ssl_info
    
    log_success "SSL配置完成！"
}

# 错误处理
trap 'log_error "SSL配置过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
