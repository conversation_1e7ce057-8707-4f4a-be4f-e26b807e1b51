# 🎬 移动端电影搜索应用

基于FastAPI构建的移动端电影搜索应用，提供WPS文档搜索和猫眼热门电影推荐功能。

## ✨ 主要特性

- 🔍 **智能搜索**: 从WPS文档中搜索电影资源链接
- 🔥 **热门推荐**: 实时获取猫眼热门电影数据
- 🔒 **安全防护**: 多层安全防护，包括频率限制、输入验证、链接加密
- 📱 **移动优化**: 响应式设计，完美适配移动设备
- 📊 **实时监控**: 内置监控面板，实时查看应用状态
- 🚀 **高性能**: 异步处理，并发优化，缓存机制

## 🏗️ 技术架构

- **后端框架**: FastAPI (Python 3.9+)
- **数据处理**: BeautifulSoup4, requests, aiohttp
- **安全组件**: cryptography, 自定义安全中间件
- **监控系统**: psutil, 自定义监控模块
- **前端技术**: 原生HTML/CSS/JavaScript

## 📦 快速部署

### VPS一键部署

```bash
# 克隆项目
git clone <repository-url>
cd movie-search-app

# 一键部署（推荐）
./scripts/deploy_all.sh yourdomain.com <EMAIL>

# 或分步部署
./scripts/deploy_vps.sh production
./scripts/setup_service.sh
./scripts/setup_nginx.sh yourdomain.com
./scripts/setup_ssl.sh yourdomain.com <EMAIL>
```

### 手动部署

1. **环境准备**
```bash
# 安装Python 3.9+
sudo apt update
sudo apt install python3 python3-pip python3-venv nginx supervisor

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置相关参数
```

3. **启动应用**
```bash
# 开发环境
python -m uvicorn app.main:app --host 0.0.0.0 --port 8506 --reload

# 生产环境
python -m uvicorn app.main:app --host 127.0.0.1 --port 8506 --workers 2
```

## 🔧 配置说明

### 环境变量配置

主要配置项（详见 `.env.example`）：

```bash
# 基础配置
ENVIRONMENT=production
HOST=0.0.0.0
PORT=8506
WORKERS=2

# 安全配置
ALLOWED_ORIGINS=https://yourdomain.com
API_KEYS=your_api_key:User Name
LINK_ENCRYPTION_KEY=your-32-character-key

# 频率限制
SEARCH_RATE_LIMIT=3
HOT_MOVIES_RATE_LIMIT=10
```

### Nginx配置

应用使用Nginx作为反向代理，配置文件位于：
- 配置文件: `/etc/nginx/sites-available/movie-search`
- 日志文件: `/var/log/nginx/movie-search.*.log`

### 系统服务

应用作为systemd服务运行：
```bash
# 服务管理
sudo systemctl start movie-search
sudo systemctl stop movie-search
sudo systemctl restart movie-search
sudo systemctl status movie-search

# 查看日志
sudo journalctl -u movie-search -f
```

## 📊 监控和维护

### 监控面板

访问 `https://yourdomain.com/static/monitoring.html` 查看：
- 系统健康状态
- 请求统计信息
- 性能指标
- 错误日志

### API接口

- **健康检查**: `GET /health`
- **热门电影**: `GET /api/hot-movies`
- **电影搜索**: `POST /api/search`
- **监控数据**: `GET /api/monitoring/stats`

### 日志管理

日志文件位置：
- 应用日志: `/opt/movie-search/logs/app.log`
- 访问日志: `/opt/movie-search/logs/access.log`
- 错误日志: `/opt/movie-search/logs/error.log`
- 安全日志: `/opt/movie-search/logs/security.log`

## 🔒 安全特性

- **频率限制**: 防止API滥用
- **输入验证**: 严格的输入过滤和清理
- **链接加密**: 敏感链接加密传输
- **会话管理**: 基于指纹的会话验证
- **日志脱敏**: 敏感信息自动脱敏
- **HTTPS强制**: 强制使用HTTPS访问

## 🚀 性能优化

- **异步处理**: FastAPI异步框架
- **并发优化**: 多worker进程
- **缓存机制**: 热门电影数据缓存
- **静态文件**: Nginx直接服务静态资源
- **Gzip压缩**: 自动压缩响应内容

## 📱 移动端适配

- 响应式设计，适配各种屏幕尺寸
- 触摸友好的交互设计
- PWA特性支持
- 快速加载优化

## 🛠️ 开发指南

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export ENVIRONMENT=development
export RELOAD=true

# 启动开发服务器
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8506
```

### 项目结构

```
movie-search-app/
├── app/                    # 应用核心代码
│   ├── main.py            # 主应用入口
│   ├── modules/           # 功能模块
│   ├── security*.py       # 安全相关模块
│   └── monitoring*.py     # 监控相关模块
├── static/                # 静态资源
├── scripts/               # 部署脚本
├── config/                # 配置文件
├── nginx/                 # Nginx配置
└── logs/                  # 日志目录
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 请确保在生产环境中修改默认的密钥和配置！
