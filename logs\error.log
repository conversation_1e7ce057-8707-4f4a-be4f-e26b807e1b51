2025-07-27 00:24:35 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 00:24:36 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 09:58:20 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 09:58:21 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 10:12:00 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 10:13:02 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582382219
2025-07-27 10:13:02 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582382374
2025-07-27 10:13:05 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582385475
2025-07-27 10:13:06 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582386383
2025-07-27 10:13:07 - security - [31mERROR[0m - 客户端 127.0.0.1:d2ad6785 因频繁违规被临时阻止 600 秒
2025-07-27 10:13:07 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582387153
2025-07-27 10:13:12 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 594秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582392632
2025-07-27 10:13:13 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 593秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753582393971
2025-07-27 10:18:34 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 10:19:30 - main - [31mERROR[0m - HTTP异常 429: 检测到可疑请求模式 - 请求路径: http://*************:8080/api/search
2025-07-27 10:19:39 - main - [31mERROR[0m - HTTP异常 429: 检测到可疑请求模式 - 请求路径: http://*************:8080/api/search
2025-07-27 10:25:02 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 10:32:11 - main - [31mERROR[0m - 搜索API异常: max_workers must be greater than 0
2025-07-27 10:32:11 - main - [31mERROR[0m - HTTP异常 500: 搜索服务暂时不可用，请稍后重试 - 请求路径: http://localhost:8080/api/search
2025-07-27 10:32:11 - requests - [31mERROR[0m - POST /api/search - 500 - 1.370s
2025-07-27 10:34:57 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 10:44:48 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 10:50:48 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 23:48:03 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:48:03 - requests - [31mERROR[0m - GET /api/hot-movies - 500 - 0.004s
2025-07-27 23:48:06 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:48:06 - requests - [31mERROR[0m - GET /api/hot-movies - 500 - 0.004s
2025-07-27 23:48:19 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:48:19 - requests - [31mERROR[0m - POST /api/search - 500 - 0.002s
2025-07-29 00:09:04 - app.main - [31mERROR[0m - HTTP异常 400: 搜索关键词包含不支持的字符类型 - 请求路径: http://localhost:8506/api/search
