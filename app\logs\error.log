2025-07-26 15:06:09 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:11:10 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:12:09 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:18:09 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:18:25 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:27:58 - main - [31mERROR[0m - 未处理异常 [error_1753514878]: Response content longer than Content-Length - 请求路径: http://localhost:8506/static/script.js
2025-07-26 15:27:58 - main - [31mERROR[0m - 异常详情 [error_1753514878]: Traceback (most recent call last):
  File "C:\Program Files\Python310\lib\site-packages\starlette\middleware\errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "C:\Program Files\Python310\lib\site-packages\starlette\middleware\base.py", line 109, in __call__
    await response(scope, receive, send)
  File "C:\Program Files\Python310\lib\site-packages\starlette\responses.py", line 270, in __call__
    async with anyio.create_task_group() as task_group:
  File "C:\Program Files\Python310\lib\site-packages\anyio\_backends\_asyncio.py", line 597, in __aexit__
    raise exceptions[0]
  File "C:\Program Files\Python310\lib\site-packages\starlette\responses.py", line 273, in wrap
    await func()
  File "C:\Program Files\Python310\lib\site-packages\starlette\middleware\base.py", line 134, in stream_response
    return await super().stream_response(send)
  File "C:\Program Files\Python310\lib\site-packages\starlette\responses.py", line 265, in stream_response
    await send({"type": "http.response.body", "body": chunk, "more_body": True})
  File "C:\Program Files\Python310\lib\site-packages\starlette\middleware\errors.py", line 159, in _send
    await send(message)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 560, in send
    raise RuntimeError("Response content longer than Content-Length")
RuntimeError: Response content longer than Content-Length

2025-07-26 15:28:28 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:28:29 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:34:28 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:34:29 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:45:23 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:45:24 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:51:23 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:51:24 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:57:23 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 15:57:24 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:03:23 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:03:25 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:09:23 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:09:25 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:15:23 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:15:25 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:55:07 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 16:55:08 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 20:51:12 - main - [31mERROR[0m - 搜索API异常: max_workers must be greater than 0
2025-07-26 20:51:12 - main - [31mERROR[0m - HTTP异常 500: 搜索服务暂时不可用，请稍后重试 - 请求路径: http://localhost:8506/api/search
2025-07-26 20:51:12 - requests - [31mERROR[0m - POST /api/search - 500 - 1.202s
2025-07-26 20:54:12 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 20:54:13 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 23:31:46 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 23:31:47 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 23:46:42 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-26 23:46:43 - monitoring - [31mERROR[0m - 清理线程异常: 'MetricsCollector' object has no attribute 'request_history'
2025-07-27 23:39:54 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:39:54 - requests - [31mERROR[0m - POST /api/search - 500 - 0.008s
2025-07-27 23:40:14 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:40:14 - requests - [31mERROR[0m - GET /api/hot-movies - 500 - 0.001s
2025-07-27 23:40:15 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:40:15 - requests - [31mERROR[0m - POST /api/search - 500 - 0.002s
2025-07-27 23:40:15 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-27 23:40:15 - requests - [31mERROR[0m - POST /api/search - 500 - 0.001s
2025-07-27 23:40:17 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-27 23:40:17 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-27 23:54:08 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 60 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-27 23:54:27 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 60 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-27 23:55:44 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 60 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-28 00:40:13 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-28 00:40:13 - requests - [31mERROR[0m - POST /api/search - 500 - 0.002s
2025-07-28 00:40:53 - security_middleware - [31mERROR[0m - 安全中间件异常: 
2025-07-28 00:40:53 - requests - [31mERROR[0m - GET /api/search-cloudsave - 500 - 0.002s
2025-07-28 01:01:35 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 60 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-28 08:55:11 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664111064
2025-07-28 08:55:11 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664111429
2025-07-28 08:55:11 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 08:55:11 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664111746
2025-07-28 08:55:11 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664111929
2025-07-28 08:55:12 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664112075
2025-07-28 08:55:45 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 266秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 09:06:58 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664818453
2025-07-28 09:06:58 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664818737
2025-07-28 09:06:59 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:06:59 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664819504
2025-07-28 09:06:59 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664819982
2025-07-28 09:07:00 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 298秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664820519
2025-07-28 09:07:00 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 298秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664820728
2025-07-28 09:07:03 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 296秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:07:08 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 290秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664828870
2025-07-28 09:07:13 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 286秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664833482
2025-07-28 09:07:13 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 285秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664833721
2025-07-28 09:07:13 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 285秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664833932
2025-07-28 09:07:14 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 285秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664834202
2025-07-28 09:07:14 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 285秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664834390
2025-07-28 09:07:14 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 284秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664834771
2025-07-28 09:07:15 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 284秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753664835016
2025-07-28 09:07:24 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 275秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:07:26 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 272秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:08:12 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 226秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:10:46 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665046882
2025-07-28 09:10:47 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665047475
2025-07-28 09:10:48 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:10:48 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665048060
2025-07-28 09:10:54 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 293秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:10:55 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 292秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:10:57 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 290秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665057443
2025-07-28 09:12:11 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665131354
2025-07-28 09:12:11 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665131565
2025-07-28 09:12:11 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:12:11 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665131722
2025-07-28 09:12:12 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665132318
2025-07-28 09:12:12 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665132637
2025-07-28 09:12:14 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 296秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:12:16 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 295秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:12:16 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 294秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:12:17 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 294秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:12:18 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 293秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:12:22 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 288秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:14:12 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665252139
2025-07-28 09:14:13 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665253060
2025-07-28 09:14:17 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:14:17 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665257397
2025-07-28 09:14:22 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 295秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665262147
2025-07-28 09:14:23 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 293秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:14:24 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 292秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:14:25 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 292秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:14:26 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 291秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 09:14:26 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 290秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 09:15:01 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 256秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:15:07 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 249秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:15:17 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 239秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 09:15:18 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 238秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 09:16:29 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 168秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753665389160
2025-07-28 09:28:29 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666109678
2025-07-28 09:28:30 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666110395
2025-07-28 09:30:03 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666203755
2025-07-28 09:30:04 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666204232
2025-07-28 09:30:04 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:30:04 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666204481
2025-07-28 09:30:04 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666204706
2025-07-28 09:30:04 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666204858
2025-07-28 09:30:06 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 297秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:31:48 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666308101
2025-07-28 09:31:48 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666308374
2025-07-28 09:31:48 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:31:48 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666308718
2025-07-28 09:31:48 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 299秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666308861
2025-07-28 09:31:51 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 296秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:31:58 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 289秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:32:04 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 284秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666324052
2025-07-28 09:33:11 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666391570
2025-07-28 09:33:11 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666391853
2025-07-28 09:33:12 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:33:12 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666392041
2025-07-28 09:33:13 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 298秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:33:23 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 288秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 09:33:26 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 285秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666406254
2025-07-28 09:33:29 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 282秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:39:10 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753666750083
2025-07-28 09:39:15 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 60 秒后重试 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:39:23 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 300 秒
2025-07-28 09:39:23 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 300 秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:40:03 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 259秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 09:40:07 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 256秒 - 请求路径: http://localhost:8080/api/search-cloudsave
2025-07-28 10:18:40 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669120623
2025-07-28 10:18:40 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669120842
2025-07-28 10:18:43 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669123845
2025-07-28 10:18:44 - main - [31mERROR[0m - HTTP异常 429: 请求过于频繁，请等待 10 秒后重试 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669124042
2025-07-28 10:18:44 - main - [31mERROR[0m - 客户端 127.0.***.***:d2ad6785 因频繁违规被临时阻止 120 秒
2025-07-28 10:18:44 - main - [31mERROR[0m - HTTP异常 429: 因频繁请求被临时阻止 120 秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669124210
2025-07-28 10:18:44 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 119秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669124369
2025-07-28 10:18:44 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 119秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669124575
2025-07-28 10:18:44 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 119秒 - 请求路径: http://localhost:8080/api/hot-movies?t=1753669124851
2025-07-28 10:18:46 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 117秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 10:18:48 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 116秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 10:18:53 - main - [31mERROR[0m - HTTP异常 429: 客户端已被临时阻止，剩余时间: 110秒 - 请求路径: http://localhost:8080/api/search
2025-07-28 13:06:38 - modules.cloudsave_searcher - [31mERROR[0m - CloudSave搜索异常: HTTPSConnectionPool(host='cs.612625.xyz', port=9999): Read timed out. (read timeout=30)
