# 设计文档

## 概述

基于现有的WPS内容搜索器和猫眼热门数据爬虫，设计一个移动端优化的电影搜索Web应用。该应用采用FastAPI作为后端框架，提供RESTful API接口，前端使用响应式HTML/CSS/JavaScript实现，通过Docker容器化部署。

## 架构设计

### 系统架构

```mermaid
graph TB
    A[移动端浏览器] --> B[Nginx反向代理]
    B --> C[FastAPI应用]
    C --> D[WPS内容搜索模块]
    C --> E[猫眼热门数据模块]
    C --> F[链接验证模块]
    C --> G[夸克文件信息模块]
    
    D --> H[WPS在线文档]
    E --> I[猫眼网站]
    F --> J[百度网盘API]
    F --> K[夸克网盘API]
    G --> K
    
    C --> L[静态文件服务]
    L --> M[HTML/CSS/JS文件]
```

### 技术栈

- **后端框架**: FastAPI (Python 3.9+)
- **前端技术**: HTML5 + CSS3 + Vanilla JavaScript
- **HTTP服务器**: Uvicorn
- **容器化**: Docker + Docker Compose
- **依赖管理**: pip + requirements.txt
- **响应式框架**: 自定义CSS Grid/Flexbox

## 组件和接口

### 后端API接口

#### 1. 热门电影接口
```python
GET /api/hot-movies
Response: {
    "movies": ["电影名1", "电影名2", ...],
    "last_update": "2025-01-26T10:00:00Z",
    "count": 15
}
```

#### 2. 搜索接口
```python
POST /api/search
Request: {
    "keyword": "电影名称"
}
Response: {
    "results": [
        {
            "title": "电影标题",
            "links": [
                {
                    "url": "https://pan.quark.cn/s/xxx",
                    "type": "quark",
                    "video_count": 12,
                    "folder_name": "文件夹名称",
                    "update_time": "2025-01-26"
                },
                {
                    "url": "https://pan.baidu.com/s/xxx",
                    "type": "baidu"
                }
            ]
        }
    ],
    "total": 1,
    "search_time": 2.5
}
```

#### 3. 健康检查接口
```python
GET /health
Response: {
    "status": "healthy",
    "timestamp": "2025-01-26T10:00:00Z"
}
```

### 前端组件

#### 1. 主页面组件 (index.html)
- 响应式布局容器
- 热门推荐标签区域
- 搜索框和按钮
- 结果显示区域
- 加载状态指示器

#### 2. 样式组件 (style.css)
- 移动端优先的响应式设计
- 现代化UI风格
- 触摸友好的交互元素
- 暗色/亮色主题支持

#### 3. 脚本组件 (script.js)
- API调用封装
- DOM操作和事件处理
- 搜索结果渲染
- 错误处理和用户反馈

## 数据模型

### 搜索结果模型
```python
class SearchResult:
    title: str                    # 电影标题
    links: List[LinkInfo]         # 有效链接列表
    video_count_total: int        # 总视频文件数量（用于排序）

class LinkInfo:
    url: str                      # 链接地址
    type: str                     # 链接类型 (quark/baidu)
    video_count: Optional[int]    # 视频文件数量
    folder_name: Optional[str]    # 文件夹名称
    update_time: Optional[str]    # 更新时间
    is_valid: bool               # 链接有效性
```

### 热门电影模型
```python
class HotMovies:
    movies: List[str]            # 电影名称列表
    last_update: datetime        # 最后更新时间
    update_interval: int         # 更新间隔（小时）
```

## 错误处理

### 错误类型定义
```python
class APIError(Exception):
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message

# 具体错误类型
class SearchTimeoutError(APIError): pass
class InvalidLinkError(APIError): pass
class NetworkError(APIError): pass
```

### 错误响应格式
```python
{
    "error": {
        "code": 500,
        "message": "搜索超时，请稍后重试",
        "type": "SearchTimeoutError"
    }
}
```

### 前端错误处理策略
- 网络错误：显示重试按钮
- 搜索无结果：显示友好提示
- 服务器错误：显示错误信息和联系方式
- 加载超时：自动重试机制

## 测试策略

### 单元测试
- API接口测试
- 数据模型验证
- 链接验证逻辑测试
- 搜索算法测试

### 集成测试
- 端到端搜索流程测试
- 第三方API集成测试
- Docker容器部署测试

### 性能测试
- 并发搜索请求测试
- 链接验证性能测试
- 移动端响应时间测试

### 移动端测试
- 不同屏幕尺寸适配测试
- 触摸交互测试
- 网络环境测试（3G/4G/WiFi）

## 部署设计

### Docker配置

#### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8506

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8506"]
```

#### docker-compose.yml
```yaml
version: '3.8'
services:
  movie-search-app:
    build: .
    ports:
      - "8506:8506"
    environment:
      - PYTHONPATH=/app
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8506/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 环境配置
- 生产环境：优化性能，启用日志
- 开发环境：启用调试模式，热重载
- 测试环境：模拟数据，快速响应

## 性能优化

### 后端优化
- 异步处理搜索请求
- 链接验证并行化
- 结果缓存机制（Redis可选）
- 数据库连接池

### 前端优化
- 资源压缩和合并
- 图片懒加载
- 搜索防抖处理
- 结果分页加载

### 网络优化
- HTTP/2支持
- Gzip压缩
- 静态资源CDN
- API响应缓存

## 安全考虑

### 输入验证
- 搜索关键词长度限制
- 特殊字符过滤
- SQL注入防护
- XSS攻击防护

### 访问控制
- 请求频率限制
- IP白名单（可选）
- CORS配置
- HTTPS强制重定向

### 数据保护
- 敏感信息脱敏
- 日志安全处理
- 第三方API密钥保护

## 监控和日志

### 日志策略
- 结构化日志格式
- 不同级别日志分离
- 敏感信息过滤
- 日志轮转机制

### 监控指标
- API响应时间
- 错误率统计
- 搜索成功率
- 系统资源使用率

### 健康检查
- 应用状态检查
- 依赖服务检查
- 数据库连接检查
- 第三方API可用性检查