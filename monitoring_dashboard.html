<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控仪表板</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .metric:last-child { border-bottom: none; }
        .status-healthy { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #e74c3c; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
        .loading { text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 电影搜索应用 - 系统监控</h1>
    </div>

    <div class="card">
        <h3>系统健康状态 <button class="refresh-btn" onclick="loadHealth()">刷新</button></h3>
        <div id="health-status" class="loading">加载中...</div>
    </div>

    <div class="card">
        <h3>请求统计 <button class="refresh-btn" onclick="loadStats()">刷新</button></h3>
        <div id="request-stats" class="loading">加载中...</div>
    </div>

    <div class="card">
        <h3>告警信息</h3>
        <div id="alerts" class="loading">加载中...</div>
    </div>

    <script>
        async function loadHealth() {
            try {
                const response = await fetch('/api/monitoring/health');
                const data = await response.json();
                
                const statusClass = data.status === 'healthy' ? 'status-healthy' : 
                                  data.status === 'degraded' ? 'status-warning' : 'status-error';
                
                document.getElementById('health-status').innerHTML = `
                    <div class="metric">
                        <span>状态</span>
                        <span class="${statusClass}">${data.status}</span>
                    </div>
                    <div class="metric">
                        <span>运行时间</span>
                        <span>${Math.floor(data.uptime_seconds / 3600)}小时</span>
                    </div>
                    <div class="metric">
                        <span>总请求数</span>
                        <span>${data.stats_summary.total_requests}</span>
                    </div>
                    <div class="metric">
                        <span>错误率</span>
                        <span class="${data.stats_summary.error_rate > 5 ? 'status-error' : 'status-healthy'}">${data.stats_summary.error_rate.toFixed(2)}%</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('health-status').innerHTML = '<div style="color: red;">加载失败</div>';
            }
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/monitoring/stats?time_window=3600');
                const data = await response.json();
                const stats = data.request_stats;
                
                document.getElementById('request-stats').innerHTML = `
                    <div class="metric">
                        <span>每分钟请求数</span>
                        <span>${stats.requests_per_minute.toFixed(1)}</span>
                    </div>
                    <div class="metric">
                        <span>平均响应时间</span>
                        <span class="${stats.avg_response_time > 3 ? 'status-error' : 'status-healthy'}">${stats.avg_response_time.toFixed(3)}s</span>
                    </div>
                    <div class="metric">
                        <span>错误率</span>
                        <span class="${stats.error_rate > 5 ? 'status-error' : 'status-healthy'}">${stats.error_rate.toFixed(2)}%</span>
                    </div>
                `;
            } catch (error) {
                document.getElementById('request-stats').innerHTML = '<div style="color: red;">加载失败</div>';
            }
        }

        async function loadAlerts() {
            try {
                const response = await fetch('/api/monitoring/alerts');
                const data = await response.json();
                
                if (data.alerts.length === 0) {
                    document.getElementById('alerts').innerHTML = '<div class="status-healthy">无告警</div>';
                } else {
                    const alertsHtml = data.alerts.map(alert => 
                        `<div class="metric"><span>${alert.type}</span><span class="status-${alert.level}">${alert.message}</span></div>`
                    ).join('');
                    document.getElementById('alerts').innerHTML = alertsHtml;
                }
            } catch (error) {
                document.getElementById('alerts').innerHTML = '<div style="color: red;">加载失败</div>';
            }
        }

        // 初始化
        loadHealth();
        loadStats();
        loadAlerts();

        // 自动刷新
        setInterval(() => {
            loadHealth();
            loadStats();
            loadAlerts();
        }, 30000);
    </script>
</body>
</html>