<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>电影搜索 - 移动端</title>
    <meta name="description" content="基于WPS内容搜索和猫眼热门数据的电影资源搜索应用">
    <meta name="keywords" content="电影搜索,网盘资源,猫眼热门,移动端">
    <!-- 使用压缩版本的 CSS -->
    <link rel="stylesheet" href="/static/style.min.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/static/script.min.js" as="script">
    <!-- DNS 预解析 -->
    <link rel="dns-prefetch" href="//api">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎬 电影搜索</h1>
            <p class="subtitle">发现热门影视资源</p>
        </header>
        
        <main>
            <!-- 热门推荐区域 -->
            <section class="hot-section">
                <div class="section-header">
                    <h2>🔥 热门推荐</h2>
                    <span id="hot-update-time" class="update-time"></span>
                </div>
                <div id="hot-movies" class="hot-movies">
                    <div class="loading">正在加载热门电影...</div>
                </div>
            </section>
            
            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="section-header">
                    <h2>🔍 搜索关键词</h2>
                </div>
                <div class="search-container">
                    <div class="input-wrapper">
                        <input type="text" id="search-input" placeholder="输入影视剧名称..." maxlength="50" autocomplete="off">
                        <span class="input-counter">0/50</span>
                    </div>
                    <button id="search-btn" type="button">
                        <span class="btn-text">搜索</span>
                        <span class="btn-icon">🔍</span>
                    </button>
                </div>
            </section>
            
            <!-- 搜索结果区域 -->
            <section class="results-section" id="results-section" style="display: none;">
                <div class="section-header">
                    <h2>📋 搜索结果</h2>
                    <div id="search-stats" class="search-stats"></div>
                </div>
                <div id="search-results" class="search-results">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </section>
        </main>
        
        <!-- 页脚 -->
        <footer>
            <p>&copy; 2025 电影搜索应用 | 数据来源：猫眼电影</p>
        </footer>
    </div>
    
    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
        <p>搜索中，请稍候...</p>
    </div>
    
    <!-- 错误提示 -->
    <div id="toast" class="toast hidden">
        <span id="toast-message"></span>
    </div>
    
    <!-- 使用压缩版本的 JavaScript -->
    <script src="/static/script.min.js"></script>
</body>
</html>