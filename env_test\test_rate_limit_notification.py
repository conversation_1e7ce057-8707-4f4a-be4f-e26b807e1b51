#!/usr/bin/env python3
"""
测试频率限制通知功能
"""

import requests
import json
import time
import hashlib

def generate_browser_fingerprint():
    """生成简单的浏览器指纹"""
    fingerprint_data = "test_browser_fingerprint_12345"
    return hashlib.md5(fingerprint_data.encode()).hexdigest()

def get_session_token():
    """获取会话令牌"""
    fingerprint = generate_browser_fingerprint()
    
    try:
        response = requests.post(
            "http://localhost:8080/api/session",
            headers={"Content-Type": "application/json"},
            json={
                "fingerprint": fingerprint,
                "timestamp": int(time.time() * 1000)
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("token"), fingerprint
        else:
            print(f"获取会话令牌失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"获取会话令牌异常: {e}")
        return None, None

def test_rate_limit_notification():
    """测试频率限制通知功能"""
    
    print("⚠️ 测试频率限制通知功能")
    print("=" * 50)
    
    # 获取会话令牌
    token, fingerprint = get_session_token()
    if not token:
        print("❌ 无法获取会话令牌，测试终止")
        return
    
    print(f"✅ 获取会话令牌成功: {token[:16]}...")
    
    # 快速发送多个搜索请求来触发频率限制
    print(f"\n🚀 快速发送搜索请求以触发频率限制...")
    
    search_count = 0
    rate_limited = False
    
    for i in range(5):  # 发送5次请求，应该会触发限制
        try:
            print(f"📤 发送第 {i+1} 次搜索请求...")
            
            response = requests.post(
                "http://localhost:8080/api/search",
                headers={
                    "Content-Type": "application/json",
                    "X-Session-Token": token,
                    "X-Browser-Fingerprint": fingerprint,
                    "X-Timestamp": str(int(time.time() * 1000))
                },
                json={
                    "keyword": f"测试电影{i+1}"
                }
            )
            
            if response.status_code == 200:
                search_count += 1
                print(f"✅ 第 {i+1} 次搜索成功")
            elif response.status_code == 429:
                rate_limited = True
                error_data = response.json()
                error_message = error_data.get('detail', '频率限制')
                
                print(f"⚠️ 第 {i+1} 次搜索触发频率限制!")
                print(f"📋 服务器返回: {error_message}")
                
                # 分析错误消息
                time_match = None
                import re
                time_match = re.search(r'(\d+)秒', error_message)
                if time_match:
                    remaining_time = int(time_match.group(1))
                    minutes = remaining_time // 60
                    seconds = remaining_time % 60
                    print(f"⏰ 剩余等待时间: {minutes}分{seconds}秒")
                
                break
            else:
                print(f"❌ 第 {i+1} 次搜索失败: {response.status_code}")
                
            # 短暂延迟
            time.sleep(0.5)
            
        except Exception as e:
            print(f"❌ 第 {i+1} 次搜索异常: {e}")
    
    # 总结测试结果
    print(f"\n📊 测试结果总结:")
    print(f"✅ 成功搜索次数: {search_count}")
    print(f"⚠️ 是否触发频率限制: {'是' if rate_limited else '否'}")
    
    if rate_limited:
        print(f"\n🎉 频率限制功能正常工作!")
        print(f"📝 前端应该显示: '搜索过于频繁，请等待 X 分钟后再试'")
        print(f"🔔 通知类型: warning (黄色边框，警告图标)")
        print(f"⏰ 显示时间: 5秒 (比普通通知更长)")
    else:
        print(f"\n⚠️ 未触发频率限制，可能需要更多请求")
    
    print(f"\n💡 用户体验改进:")
    print(f"✅ 用户现在会看到明确的频率限制提示")
    print(f"✅ 不会误以为是'没有搜索结果'")
    print(f"✅ 知道需要等待多长时间")
    print(f"✅ 有清晰的视觉反馈（黄色警告）")

if __name__ == "__main__":
    test_rate_limit_notification()
