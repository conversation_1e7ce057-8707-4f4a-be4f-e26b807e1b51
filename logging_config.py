"""
日志配置模块
"""

import logging
import logging.handlers
import os
import json
from datetime import datetime
from typing import Dict, Any

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        # 创建结构化日志记录
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'client_ip'):
            log_entry['client_ip'] = record.client_ip
        
        if hasattr(record, 'user_agent'):
            log_entry['user_agent'] = record.user_agent
        
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'response_time'):
            log_entry['response_time'] = record.response_time
        
        return json.dumps(log_entry, ensure_ascii=False)

class LoggingConfig:
    """日志配置类"""
    
    @staticmethod
    def setup_logging(log_level: str = "INFO", log_dir: str = "logs"):
        """设置日志配置"""
        
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        root_logger.addHandler(console_handler)
        
        # 应用日志文件处理器
        app_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join(log_dir, 'app.log'),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        app_handler.setFormatter(console_formatter)
        app_handler.setLevel(logging.INFO)
        root_logger.addHandler(app_handler)
        
        # 错误日志文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join(log_dir, 'error.log'),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setFormatter(console_formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
        
        # 安全日志文件处理器（结构化格式）
        security_logger = logging.getLogger('security')
        security_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join(log_dir, 'security.log'),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        security_handler.setFormatter(StructuredFormatter())
        security_handler.setLevel(logging.INFO)
        security_logger.addHandler(security_handler)
        security_logger.propagate = False  # 不传播到根日志记录器
        
        # 访问日志文件处理器
        access_logger = logging.getLogger('access')
        access_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join(log_dir, 'access.log'),
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        access_formatter = logging.Formatter(
            '%(asctime)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        access_handler.setFormatter(access_formatter)
        access_handler.setLevel(logging.INFO)
        access_logger.addHandler(access_handler)
        access_logger.propagate = False
        
        # 性能日志文件处理器
        performance_logger = logging.getLogger('performance')
        performance_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join(log_dir, 'performance.log'),
            maxBytes=20 * 1024 * 1024,  # 20MB
            backupCount=5,
            encoding='utf-8'
        )
        performance_handler.setFormatter(StructuredFormatter())
        performance_handler.setLevel(logging.INFO)
        performance_logger.addHandler(performance_handler)
        performance_logger.propagate = False
        
        logging.info(f"日志系统初始化完成 - 日志级别: {log_level}, 日志目录: {log_dir}")

class AccessLogger:
    """访问日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger('access')
    
    def log_request(self, method: str, path: str, status_code: int, 
                   response_time: float, client_ip: str, user_agent: str = "",
                   request_size: int = 0, response_size: int = 0):
        """记录访问日志"""
        log_message = (
            f'{client_ip} - "{method} {path}" {status_code} '
            f'{response_size} {response_time:.3f}s "{user_agent}"'
        )
        
        self.logger.info(log_message)

class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger('performance')
    
    def log_slow_request(self, method: str, path: str, response_time: float, 
                        client_id: str, details: Dict[str, Any] = None):
        """记录慢请求"""
        log_data = {
            'event_type': 'slow_request',
            'method': method,
            'path': path,
            'response_time': response_time,
            'client_id': client_id,
            'details': details or {}
        }
        
        self.logger.warning(json.dumps(log_data, ensure_ascii=False))
    
    def log_high_memory_usage(self, memory_percent: float, memory_used_mb: float):
        """记录高内存使用"""
        log_data = {
            'event_type': 'high_memory_usage',
            'memory_percent': memory_percent,
            'memory_used_mb': memory_used_mb,
            'timestamp': datetime.now().isoformat()
        }
        
        self.logger.warning(json.dumps(log_data, ensure_ascii=False))
    
    def log_high_cpu_usage(self, cpu_percent: float):
        """记录高CPU使用"""
        log_data = {
            'event_type': 'high_cpu_usage',
            'cpu_percent': cpu_percent,
            'timestamp': datetime.now().isoformat()
        }
        
        self.logger.warning(json.dumps(log_data, ensure_ascii=False))

# 创建全局日志记录器实例
access_logger = AccessLogger()
performance_logger = PerformanceLogger()