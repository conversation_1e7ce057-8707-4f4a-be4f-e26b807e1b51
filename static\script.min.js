class MovieSearchApp{constructor(){this.apiBase = '/api';this.searchInput = document.getElementById('search-input');this.searchForm = document.getElementById('search-form');this.searchBtn1 = document.getElementById('search-btn-1');this.searchBtn2 = document.getElementById('search-btn-2');this.hotMoviesContainer = document.getElementById('hot-movies');this.resultsSection = document.getElementById('results-section');this.searchResults = document.getElementById('search-results');this.resultsCount = document.getElementById('results-count');this.searchStats = document.getElementById('search-stats');this.loadingOverlay = document.getElementById('loading-overlay');this.notification = document.getElementById('notification');this.hotUpdateTime = document.getElementById('hot-update-time');this.inputCounter = document.getElementById('input-counter');this.emptyState = document.getElementById('empty-state');this.activeSearchType = 'wps';this.currentSearchTerm = '';this.allSearchResults = [];this.isSearching = false;this.init();}init(){this.bindEvents();this.loadHotMovies();this.setupInputCounter();}bindEvents(){this.searchBtn1.addEventListener('click',()=>{this.setActiveSearchType('wps');this.searchMovies();});this.searchBtn2.addEventListener('click',()=>{this.setActiveSearchType('cloudsave');this.searchMovies();});this.searchForm.addEventListener('submit',(e)=>{e.preventDefault();this.searchMovies();});this.searchInput.addEventListener('input',()=>{this.updateInputCounter();});const notificationClose = document.getElementById('notification-close');if(notificationClose){notificationClose.addEventListener('click',()=>{this.hideNotification();});}}setActiveSearchType(searchType){this.activeSearchType = searchType;if(searchType === 'wps'){this.searchBtn1.classList.add('active');this.searchBtn2.classList.remove('active');}else{this.searchBtn1.classList.remove('active');this.searchBtn2.classList.add('active');}}setupInputCounter(){this.updateInputCounter();}updateInputCounter(){const length = this.searchInput.value.length;this.inputCounter.textContent = `${length}/50`;if(length > 40){this.inputCounter.style.color = '#EF4444';}else if(length > 30){this.inputCounter.style.color = '#F59E0B';}else{this.inputCounter.style.color = '#9CA3AF';}}async loadHotMovies(){try{const timestamp = new Date().getTime();const response = await fetch(`${this.apiBase}/hot-movies?t=${timestamp}`);const data = await response.json();if(data.success && data.data){this.renderHotMovies(data.data);if(data.update_time){this.hotUpdateTime.textContent = `更新时间: ${new Date(data.update_time).toLocaleString()}`;}}else{this.renderDefaultHotMovies();}}catch(error){console.error('获取热门电影失败:',error);this.renderDefaultHotMovies();}}renderHotMovies(movies){if(!movies || movies.length === 0){this.renderDefaultHotMovies();return;}this.hotMoviesContainer.innerHTML = movies.map((movie,index)=>{const isHot = index < 3;return ` <button class="hot-movie-btn ${isHot ? 'hot' : 'normal'}" onclick="app.searchHotMovie('${movie.replace(/'/g,"\\'")}')" > ${movie}</button> `;}).join('');}renderDefaultHotMovies(){const defaultMovies = ['流浪地球3','热辣滚烫','独行月球2','熊出没','消失的她'];this.renderHotMovies(defaultMovies);}searchHotMovie(movieName){this.searchInput.value = movieName;this.updateInputCounter();this.setActiveSearchType('wps');this.searchMovies();}async searchMovies(){const keyword = this.searchInput.value.trim();if(!keyword){this.showNotification('提示','请输入搜索关键词','error');return;}if(this.isSearching)return;this.isSearching = true;this.currentSearchTerm = keyword;this.showLoading();this.updateSearchButton(true);try{const endpoint = this.activeSearchType === 'wps' ? '/search' : '/search-cloudsave';const response = await fetch(`${this.apiBase}${endpoint}`,{method: 'POST',headers:{'Content-Type': 'application/json',},body: JSON.stringify({keyword})});const data = await response.json();if(data.success){if(this.activeSearchType === 'wps'){this.allSearchResults = data.data || [];}else{this.allSearchResults = this.formatCloudsaveResults(data.data ||{});}this.renderResults();this.showNotification('成功',`找到 ${this.allSearchResults.length}个结果`,'success');}else{throw new Error(data.error?.message || '搜索失败');}}catch(error){console.error('搜索失败:',error);this.showNotification('错误',error.message || '搜索失败，请稍后重试','error');this.allSearchResults = [];this.renderResults();}finally{this.isSearching = false;this.hideLoading();this.updateSearchButton(false);}}formatCloudsaveResults(cloudsaveData){const results = [];const cloudTypeMap ={'quark':{name: '夸克网盘',icon: '🟢',color: 'green'},'baidu':{name: '百度网盘',icon: '🔵',color: 'blue'},'aliyun':{name: '阿里云盘',icon: '🟡',color: 'yellow'},'tianyi':{name: '天翼云盘',icon: '🟠',color: 'orange'},'xunlei':{name: '迅雷网盘',icon: '🔴',color: 'red'},'uc':{name: 'UC网盘',icon: '🟣',color: 'purple'},'pan123':{name: '123网盘',icon: '⚫',color: 'gray'},'115':{name: '115网盘',icon: '🟤',color: 'brown'},'lanzou':{name: '蓝奏云',icon: '🔷',color: 'cyan'},'weiyun':{name: '微云',icon: '🟢',color: 'green'},'caiyun':{name: '彩云',icon: '🌈',color: 'rainbow'}};for(const [cloudType,items] of Object.entries(cloudsaveData)){const cloudInfo = cloudTypeMap[cloudType] ||{name: cloudType,icon: '💾',color: 'gray'};items.forEach(item =>{results.push({title: item.title,links: [{type: cloudType,url: item.link,video_count: item.video_count || null}],video_count_total: item.video_count || 0,episode: item.episode,cloudType: cloudType,cloudInfo: cloudInfo,pubDate: item.pubDate,channel: item.channel});});}return results;}sortByVideoCount(results){return results.sort((a,b)=>{const aVideoCount = a.video_count_total || 0;const bVideoCount = b.video_count_total || 0;if(aVideoCount > 0 && bVideoCount === 0)return -1;if(bVideoCount > 0 && aVideoCount === 0)return 1;if(aVideoCount !== bVideoCount){return bVideoCount - aVideoCount;}const aLinkCount =(a.links || []).length;const bLinkCount =(b.links || []).length;return bLinkCount - aLinkCount;});}renderResults(){const sortedResults = this.sortByVideoCount([...this.allSearchResults]);const validResults = sortedResults.filter(result =>{if(!result.links || result.links.length === 0){return false;}const validLinks = result.links.filter(link =>{if(link.type === 'quark'){const videoCount = link.video_count;return videoCount !== null && videoCount !== undefined && videoCount > 0;}return true;});return validLinks.length > 0;});if(validResults.length === 0){this.resultsSection.classList.add('hidden');this.emptyState.classList.remove('hidden');return;}this.emptyState.classList.add('hidden');this.resultsSection.classList.remove('hidden');this.resultsCount.textContent = `(${validResults.length})`;this.searchResults.innerHTML = validResults.map((result,index)=>{return this.renderResultCard(result,index);}).join('');this.searchResults.classList.add('fade-in');}renderResultCard(result,index){if(result.cloudType && result.cloudInfo){return this.renderCloudsaveCard(result,index);}const hasLinks = result.links && result.links.length > 0;const videoCount = result.video_count_total || 0;const hasValidQuarkLinks = result.links && result.links.some(link =>{if(link.type === 'quark'){const linkVideoCount = link.video_count;return linkVideoCount !== null && linkVideoCount !== undefined && linkVideoCount > 0;}return false;});return ` <div class="result-card slide-up" style="animation-delay: ${index * 0.1}s"> <div class="result-title">${this.escapeHtml(result.title)}</div> <div class="result-stats"> <div>总链接: ${(result.links || []).length}</div> ${hasValidQuarkLinks ? `<div class="video-count-display">夸克视频文件: ${videoCount > 0 ? videoCount : '未知'}</div>` : ''}</div> ${hasLinks ? this.renderLinks(result.links): this.renderNoLinks()}</div> `;}renderCloudsaveCard(result,index){const cloudInfo = result.cloudInfo;const videoCount = result.video_count_total || 0;const episode = result.episode;return ` <div class="result-card slide-up" style="animation-delay: ${index * 0.1}s"> <div class="cloudsave-header"> <span class="cloud-icon">${cloudInfo.icon}</span> <span class="cloud-name">${cloudInfo.name}</span> </div> <div class="result-title">${this.escapeHtml(result.title)}</div> <div class="cloudsave-stats"> ${episode ? `<span>集数: ${episode}</span>` : ''}${result.cloudType === 'quark' && videoCount > 0 ? `<span>夸克视频文件: ${videoCount}</span>` : ''}<span>链接检测有效</span> </div> <div class="cloudsave-actions"> <a href="${result.links[0].url}" target="_blank" rel="noopener noreferrer" class="link-btn primary"> 打开链接 </a> </div> </div> `;}renderLinks(links){const validLinks = links.filter(link =>{if(link.type === 'quark'){const videoCount = link.video_count;return videoCount !== null && videoCount !== undefined && videoCount > 0;}return true;});if(validLinks.length === 0){return this.renderNoLinks();}return ` <div class="result-links"> ${validLinks.map(link => this.renderLinkItem(link)).join('')}</div> `;}renderLinkItem(link){const linkType = link.type === 'baidu' ? 'baidu' : 'quark';const linkTypeName = link.type === 'baidu' ? '百度网盘' : '夸克网盘';return ` <div class="link-item"> <div class="link-info"> <div> <span class="link-type ${linkType}">${linkTypeName}</span> <span class="link-name">链接检测有效</span> </div> </div> <div class="link-actions"> <a href="${link.url}" target="_blank" rel="noopener noreferrer" class="link-btn primary"> 打开链接 </a> </div> </div> `;}renderNoLinks(){return ` <div class="result-links"> <div class="text-center py-4 text-gray-500"> <div class="text-2xl mb-2">🔗</div> <div>暂无有效链接</div> </div> </div> `;}showLoading(){this.loadingOverlay.classList.remove('hidden');}hideLoading(){this.loadingOverlay.classList.add('hidden');}updateSearchButton(isLoading){const activeBtn = this.activeSearchType === 'wps' ? this.searchBtn1 : this.searchBtn2;const btnText = activeBtn.querySelector('.btn-text');const btnIcon = activeBtn.querySelector('.btn-icon');if(isLoading){btnText.textContent = '搜索中...';btnIcon.textContent = '⏳';this.searchBtn1.disabled = true;this.searchBtn2.disabled = true;}else{this.searchBtn1.querySelector('.btn-text').textContent = '搜索一';this.searchBtn1.querySelector('.btn-icon').textContent = '🔍';this.searchBtn2.querySelector('.btn-text').textContent = '搜索二';this.searchBtn2.querySelector('.btn-icon').textContent = '☁️';this.searchBtn1.disabled = false;this.searchBtn2.disabled = false;}}showNotification(title,message,type = 'success'){const messageEl = document.getElementById('notification-message');const successIcon = document.getElementById('notification-icon-success');const errorIcon = document.getElementById('notification-icon-error');let displayMessage = message;if(type === 'success' && message.includes('找到')){displayMessage = message.replace('找到 ','').replace(' 个结果','个结果');}messageEl.textContent = displayMessage;this.notification.className = `fixed top-4 right-4 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 p-4 z-50 transform transition-all duration-300 ease-in-out ${type}`;if(type === 'success'){successIcon.classList.remove('hidden');errorIcon.classList.add('hidden');}else{successIcon.classList.add('hidden');errorIcon.classList.remove('hidden');}this.notification.classList.remove('hidden','hide');this.notification.classList.add('show');setTimeout(()=>{this.hideNotification();},3000);}hideNotification(){this.notification.classList.remove('show');this.notification.classList.add('hide');setTimeout(()=>{this.notification.classList.add('hidden');this.notification.classList.remove('hide');},300);}escapeHtml(text){const div = document.createElement('div');div.textContent = text;return div.innerHTML;}}let app;document.addEventListener('DOMContentLoaded',()=>{app = new MovieSearchApp();});window.addEventListener('error',(event)=>{console.error('全局错误:',event.error);});window.addEventListener('unhandledrejection',(event)=>{console.error('未处理的Promise拒绝:',event.reason);});