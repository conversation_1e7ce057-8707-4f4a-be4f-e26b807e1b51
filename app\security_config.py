"""
安全配置文件
"""

import os
from typing import List, Dict, Any

class SecurityConfig:
    """安全配置类"""
    
    # 环境配置
    ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
    
    # CORS 配置
    ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:8506,http://127.0.0.1:8506").split(",")
    
    # 生产环境使用更严格的CORS设置
    if ENVIRONMENT == "production":
        CORS_ORIGINS = ALLOWED_ORIGINS
        CORS_CREDENTIALS = False
    else:
        CORS_ORIGINS = ["*"]  # 开发环境允许所有源
        CORS_CREDENTIALS = True
    
    # 允许的HTTP方法
    ALLOWED_METHODS = ["GET", "POST", "OPTIONS"]
    
    # 允许的请求头
    ALLOWED_HEADERS = [
        "Content-Type", 
        "Authorization", 
        "X-Requested-With",
        "Accept",
        "Origin",
        "User-Agent",
        "DNT",
        "Cache-Control",
        "X-Mx-ReqToken",
        "Keep-Alive",
    ]
    
    # 暴露的响应头
    EXPOSE_HEADERS = ["X-Process-Time", "X-Request-ID", "X-Client-ID"]
    
    # 预检请求缓存时间（秒）
    MAX_AGE = 86400  # 24小时
    
    # 内容安全策略
    CONTENT_SECURITY_POLICY = {
        "default-src": "'self'",
        "script-src": "'self' 'unsafe-inline'",
        "style-src": "'self' 'unsafe-inline'",
        "img-src": "'self' data: https:",
        "font-src": "'self'",
        "connect-src": "'self'",
        "frame-ancestors": "'none'",
        "base-uri": "'self'",
        "form-action": "'self'"
    }
    
    # 安全响应头
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
    }
    
    # HSTS配置（仅生产环境）
    if ENVIRONMENT == "production":
        SECURITY_HEADERS["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    # API密钥配置
    default_keys = "demo_key_123456:Demo User,frontend_key_2024:Frontend User"
    API_KEYS = {
        # 从环境变量读取API密钥，格式：key1:name1,key2:name2
        key.split(':')[0]: key.split(':')[1] if ':' in key else 'Unknown'
        for key in os.getenv("API_KEYS", default_keys).split(',')
        if key.strip()
    }

    # 是否启用API密钥认证
    ENABLE_API_KEY_AUTH = os.getenv("ENABLE_API_KEY_AUTH", "true").lower() == "true"

    # 频率限制配置 - 前端友好的限制
    RATE_LIMITS = {
        "search": {
            "requests": int(os.getenv("SEARCH_RATE_LIMIT", "3")),  # 每分钟最多3次搜索
            "window": 60,
            "burst": 3,  # 允许3次突发请求
            "burst_window": 60
        },
        "search-cloudsave": {
            "requests": int(os.getenv("CLOUDSAVE_RATE_LIMIT", "2")),  # 每分钟最多2次CloudSave搜索
            "window": 60,
            "burst": 2,  # 允许2次突发请求
            "burst_window": 60
        },
        "hot-movies": {
            "requests": int(os.getenv("HOT_MOVIES_RATE_LIMIT", "10")),  # 每分钟最多10次
            "window": 60,
            "burst": 3,
            "burst_window": 10
        },
        "default": {
            "requests": int(os.getenv("DEFAULT_RATE_LIMIT", "20")),  # 默认每分钟20次
            "window": 60,
            "burst": 5,
            "burst_window": 10
        }
    }
    
    # 阻止配置
    MAX_VIOLATIONS = int(os.getenv("MAX_VIOLATIONS", "5"))  # 最大违规次数
    BLOCK_DURATION = int(os.getenv("BLOCK_DURATION", "120"))  # 阻止时长（秒）- 2分钟
    
    # 输入验证配置
    MAX_KEYWORD_LENGTH = int(os.getenv("MAX_KEYWORD_LENGTH", "50"))
    MIN_KEYWORD_LENGTH = int(os.getenv("MIN_KEYWORD_LENGTH", "2"))
    MAX_CHAR_REPETITION = int(os.getenv("MAX_CHAR_REPETITION", "10"))
    
    # 缓存配置
    CACHE_EXPIRE_TIME = int(os.getenv("CACHE_EXPIRE_TIME", "300"))  # 5分钟
    MAX_CACHE_SIZE = int(os.getenv("MAX_CACHE_SIZE", "100"))
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_SECURITY_EVENTS = os.getenv("LOG_SECURITY_EVENTS", "true").lower() == "true"
    
    # 监控配置
    ENABLE_PERFORMANCE_MONITORING = os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true"
    SLOW_REQUEST_THRESHOLD = float(os.getenv("SLOW_REQUEST_THRESHOLD", "5.0"))  # 慢请求阈值（秒）
    
    # 可信代理配置（用于获取真实IP）
    TRUSTED_PROXIES = os.getenv("TRUSTED_PROXIES", "").split(",") if os.getenv("TRUSTED_PROXIES") else []
    
    # 地理位置限制（可选）
    ENABLE_GEO_BLOCKING = os.getenv("ENABLE_GEO_BLOCKING", "false").lower() == "true"
    BLOCKED_COUNTRIES = os.getenv("BLOCKED_COUNTRIES", "").split(",") if os.getenv("BLOCKED_COUNTRIES") else []
    
    # 用户代理过滤
    ENABLE_USER_AGENT_FILTERING = os.getenv("ENABLE_USER_AGENT_FILTERING", "true").lower() == "true"
    BLOCKED_USER_AGENTS = [
        "bot", "crawler", "spider", "scraper", "scanner",
        "curl", "wget", "python-requests", "go-http-client"
    ]
    
    # 蜜罐配置
    ENABLE_HONEYPOT = os.getenv("ENABLE_HONEYPOT", "false").lower() == "true"
    HONEYPOT_ENDPOINTS = ["/admin", "/wp-admin", "/.env", "/config"]

    # 请求签名验证配置
    ENABLE_REQUEST_SIGNATURE = os.getenv("ENABLE_REQUEST_SIGNATURE", "true").lower() == "true"
    SIGNATURE_SECRET = os.getenv("SIGNATURE_SECRET", "your-secret-key-change-in-production")
    SIGNATURE_TIMEOUT = int(os.getenv("SIGNATURE_TIMEOUT", "300"))  # 签名有效期5分钟

    # 链接加密配置
    ENABLE_LINK_ENCRYPTION = os.getenv("ENABLE_LINK_ENCRYPTION", "true").lower() == "true"
    LINK_ENCRYPTION_KEY = os.getenv("LINK_ENCRYPTION_KEY", "your-encryption-key-32-chars-long")[:32].ljust(32, '0')

    # 日志脱敏配置
    ENABLE_LOG_SANITIZATION = os.getenv("ENABLE_LOG_SANITIZATION", "true").lower() == "true"
    SENSITIVE_PATTERNS = [
        r'password=\w+',
        r'token=[\w\-]+',
        r'key=[\w\-]+',
        r'secret=[\w\-]+',
        r'api_key=[\w\-]+',
        r'(?:https?://)?(?:pan\.baidu\.com|drive\.uc\.cn)/[^\s]+',  # 网盘链接
    ]
    
    @classmethod
    def get_csp_header_value(cls) -> str:
        """获取CSP头部值"""
        return "; ".join([f"{key} {value}" for key, value in cls.CONTENT_SECURITY_POLICY.items()])
    
    @classmethod
    def is_development(cls) -> bool:
        """是否为开发环境"""
        return cls.ENVIRONMENT == "development"
    
    @classmethod
    def is_production(cls) -> bool:
        """是否为生产环境"""
        return cls.ENVIRONMENT == "production"
    
    @classmethod
    def get_rate_limit_config(cls, endpoint: str) -> Dict[str, int]:
        """获取指定接口的频率限制配置"""
        return cls.RATE_LIMITS.get(endpoint, cls.RATE_LIMITS["default"])

# 创建全局配置实例
security_config = SecurityConfig()