# Git相关
.git
.gitignore

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 临时文件
.tmp/
temp/
*.tmp

# 操作系统相关
.DS_Store
Thumbs.db

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 脚本文件
*.sh
*.bat

# 文档
README.md
docs/

# 测试相关
tests/
test_*
*_test.py
env_test/

# 工具脚本目录
scripts/

# 配置文件（如果包含敏感信息）
.env
.env.local
.env.production

# Kiro相关
.kiro/