@echo off
chcp 65001 >nul
title 移动端电影搜索应用

echo ========================================
echo 🎬 移动端电影搜索应用
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)

REM 检查是否存在虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo 🔧 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  未找到虚拟环境，使用系统Python
)

REM 设置开发环境变量
if exist ".env.dev" (
    echo 📝 加载开发环境配置...
    for /f "usebackq tokens=1,2 delims==" %%a in (".env.dev") do (
        if not "%%a"=="" if not "%%a:~0,1%"=="#" (
            set "%%a=%%b"
        )
    )
)

REM 检查依赖
echo 📦 检查依赖包...
python -c "import fastapi, uvicorn" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要依赖包
    echo 正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo.
echo 🚀 启动应用...
echo 访问地址: http://localhost:8506
echo 按 Ctrl+C 停止应用
echo ========================================
echo.

REM 启动应用
python start.py

echo.
echo 应用已停止
pause
