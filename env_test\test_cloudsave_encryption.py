#!/usr/bin/env python3
"""
测试CloudSave接口的链接加密
"""

import requests
import json
import time
import hashlib

def generate_browser_fingerprint():
    """生成简单的浏览器指纹"""
    fingerprint_data = "test_browser_fingerprint_12345"
    return hashlib.md5(fingerprint_data.encode()).hexdigest()

def get_session_token():
    """获取会话令牌"""
    fingerprint = generate_browser_fingerprint()
    
    try:
        response = requests.post(
            "http://localhost:8080/api/session",
            headers={"Content-Type": "application/json"},
            json={
                "fingerprint": fingerprint,
                "timestamp": int(time.time() * 1000)
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("token"), fingerprint
        else:
            print(f"获取会话令牌失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"获取会话令牌异常: {e}")
        return None, None

def test_cloudsave_search():
    """测试CloudSave搜索接口的链接加密"""
    
    print("🔍 测试CloudSave搜索接口链接加密")
    print("=" * 50)
    
    # 获取会话令牌
    token, fingerprint = get_session_token()
    if not token:
        print("❌ 无法获取会话令牌，测试终止")
        return
    
    print(f"✅ 获取会话令牌成功: {token[:16]}...")
    
    # 测试CloudSave搜索
    try:
        response = requests.post(
            "http://localhost:8080/api/search-cloudsave",
            headers={
                "Content-Type": "application/json",
                "X-Session-Token": token,
                "X-Browser-Fingerprint": fingerprint,
                "X-Timestamp": str(int(time.time() * 1000))
            },
            json={
                "keyword": "测试电影"
            }
        )
        
        print(f"\n📊 API响应:")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ CloudSave搜索成功!")
            print(f"搜索关键词: {data.get('keyword', '未知')}")
            print(f"云盘类型数: {data.get('total_cloud_types', 0)}")
            print(f"总资源数: {data.get('total_results', 0)}")
            print(f"搜索耗时: {data.get('search_time', 0)} 秒")
            
            # 检查安全信息
            security = data.get('security', {})
            print(f"🔐 链接加密状态: {security.get('links_encrypted', False)}")
            print(f"👤 认证用户: {security.get('authenticated_user', 'Unknown')}")
            
            # 检查链接是否加密
            search_data = data.get('data', {})
            encrypted_count = 0
            total_count = 0
            
            print(f"\n🔗 链接加密检查:")
            for cloud_type, items in search_data.items():
                print(f"\n📁 {cloud_type.upper()} 云盘:")
                for i, item in enumerate(items[:3]):  # 只检查前3个
                    total_count += 1
                    link = item.get('link', '')
                    title = item.get('title', '未知标题')[:50] + "..."
                    
                    if link.startswith('aes:'):
                        encrypted_count += 1
                        print(f"  ✅ [{i+1}] {title}")
                        print(f"      加密链接: {link[:50]}...")
                    else:
                        print(f"  ❌ [{i+1}] {title}")
                        print(f"      明文链接: {link}")
            
            print(f"\n📈 加密统计:")
            print(f"总链接数: {total_count}")
            print(f"加密链接数: {encrypted_count}")
            print(f"加密率: {(encrypted_count/total_count*100) if total_count > 0 else 0:.1f}%")
            
            if encrypted_count == total_count and total_count > 0:
                print("🎉 所有链接都已正确加密!")
            elif encrypted_count > 0:
                print("⚠️ 部分链接已加密，部分未加密")
            else:
                print("❌ 没有链接被加密!")
                
        else:
            print(f"❌ CloudSave搜索失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_cloudsave_search()
