"""
WPS链接验证模块

从现有的百度和夸克验证逻辑中提取核心功能
"""

import time
import json
import logging
import requests
import urllib.parse
import urllib3
import re
from typing import Union, Tuple, Dict, Optional

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_quark_share_url(share_url: str, name: str = "", max_retries: int = 3, retry_delay: int = 2) -> Tuple[bool, str]:
    """
    检查夸克网盘分享链接是否有效

    Args:
        share_url: 分享链接
        name: 内容名称（用于日志输出）
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）

    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
        - (True, ""): 链接有效
        - (False, "invalid"): 链接无效
        - (False, error_msg): 检查失败，返回错误信息
    """
    try:
        pwd_id = share_url.split('/s/')[-1]
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://pan.quark.cn',
            'referer': 'https://pan.quark.cn/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        }

        for attempt in range(max_retries):
            try:
                # 1. 获取token
                token_response = requests.post(
                    'https://drive-h.quark.cn/1/clouddrive/share/sharepage/token',
                    params={
                        'pr': 'ucpro',
                        'fr': 'pc',
                        '__dt': '1003',
                        '__t': str(int(time.time() * 1000)),
                    },
                    headers=headers,
                    json={'pwd_id': pwd_id},
                    verify=False,
                    timeout=10
                )

                if token_response.status_code == 404:
                    return False, "invalid"

                try:
                    token_data = token_response.json()
                except json.JSONDecodeError:
                    return False, "token_parse_error"

                # 检查错误信息
                if 'code' in token_data and token_data['code'] != 0:
                    error_msg = token_data.get('message', '未知错误')
                    return False, f"token_error: {error_msg}"

                if 'data' not in token_data or 'stoken' not in token_data['data']:
                    return False, "invalid_token_data"

                stoken = token_data['data']['stoken']

                # 2. 获取详情
                detail_url = (
                    f'https://drive-h.quark.cn/1/clouddrive/share/sharepage/detail'
                    f'?pr=ucpro&fr=pc&uc_param_str='
                    f'&pwd_id={pwd_id}'
                    f'&stoken={urllib.parse.quote(stoken)}'
                    f'&pdir_fid=0'
                    f'&force=0'
                    f'&_page=1'
                    f'&_size=50'
                    f'&_fetch_banner=1'
                    f'&_fetch_share=1'
                    f'&_fetch_total=1'
                    f'&_sort=file_type:asc,updated_at:desc'
                    f'&__dt=633'
                    f'&__t={str(int(time.time() * 1000))}'
                )

                response = requests.get(
                    url=detail_url,
                    headers=headers,
                    verify=False,
                    timeout=10
                )

                try:
                    data = response.json()
                except json.JSONDecodeError:
                    return False, "detail_parse_error"

                # 检查错误信息
                if 'code' in data and data['code'] != 0:
                    error_msg = data.get('message', '未知错误')
                    return False, f"detail_error: {error_msg}"

                if "data" not in data:
                    return False, "invalid_detail_data"

                share_info = data["data"].get("share", {})
                total = data.get("metadata", {}).get("_total", 0)

                # 修改判断逻辑：只有当download_pvlimited为False且total>=1时才认为链接有效
                if share_info.get('download_pvlimited', True) == False and total >= 1:
                    return True, ""  # 链接有效

                return False, "invalid"  # 其他情况都认为链接失效

            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    error_msg = f"检查失败，已重试 {max_retries} 次"
                    return False, error_msg

    except Exception as e:
        error_msg = f"链接格式错误: {str(e)}"
        return False, error_msg


def check_baidu_share_url(share_url: str, name: str = "", max_retries: int = 3, retry_delay: int = 2) -> Tuple[bool, str]:
    """
    检查百度网盘分享链接是否有效

    Args:
        share_url: 分享链接
        name: 内容名称（用于日志输出）
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）

    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
        - (True, ""): 链接有效
        - (False, "invalid"): 链接无效
        - (False, error_msg): 检查失败，返回错误信息
    """
    try:
        # 百度网盘请求头
        baidu_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # 提取链接中的密码
        pwd_match = re.search(r'\?pwd=(\w+)', share_url)
        pwd = pwd_match.group(1) if pwd_match else None

        # 如果URL中包含密码，去除密码部分
        if pwd:
            share_url = share_url.split('?')[0]

        params = {'pwd': pwd} if pwd else {}

        for attempt in range(max_retries):
            try:
                response = requests.get(
                    url=share_url,
                    params=params,
                    headers=baidu_headers,
                    timeout=10
                )

                if response.status_code == 404 or "百度网盘-链接不存在" in response.text:
                    return False, "invalid"

                return True, ""

            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    error_msg = f"检查失败，已重试 {max_retries} 次"
                    return False, error_msg

    except Exception as e:
        error_msg = f"链接格式错误: {str(e)}"
        return False, error_msg


def validate_link(url: str, name: str = "") -> Tuple[bool, str]:
    """
    通用链接验证函数，自动识别链接类型并调用相应的验证函数

    Args:
        url: 要验证的链接
        name: 内容名称（用于日志输出）

    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    if "pan.quark.cn" in url:
        return check_quark_share_url(url, name)
    elif "pan.baidu.com" in url:
        return check_baidu_share_url(url, name)
    else:
        return False, "unsupported_link_type"