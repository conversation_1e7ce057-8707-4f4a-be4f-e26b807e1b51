"""
API认证和签名验证模块
"""

import hashlib
import hmac
import time
import base64
import json
import logging
from typing import Optional, <PERSON><PERSON>
from fastapi import Request, HTTPException
from security_config import security_config

logger = logging.getLogger(__name__)

class APIAuthenticator:
    """API认证器"""
    
    def __init__(self):
        self.api_keys = security_config.API_KEYS
        self.enable_api_key_auth = security_config.ENABLE_API_KEY_AUTH
        self.enable_signature = security_config.ENABLE_REQUEST_SIGNATURE
        self.signature_timeout = security_config.SIGNATURE_TIMEOUT

        # 不同API密钥使用不同的签名密钥
        self.signature_secrets = {
            'demo_key_123456': security_config.SIGNATURE_SECRET,
            'frontend_key_2024': 'frontend_secret_key_for_web_ui_only',
            # 默认密钥
            'default': security_config.SIGNATURE_SECRET
        }
    
    def validate_api_key(self, api_key: str) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 用户名)
        """
        if not self.enable_api_key_auth:
            return True, "Anonymous"
        
        if not api_key:
            return False, None
        
        if api_key in self.api_keys:
            return True, self.api_keys[api_key]
        
        return False, None
    
    def generate_signature(self, method: str, path: str, timestamp: str, body: str = "", api_key: str = None) -> str:
        """
        生成请求签名

        Args:
            method: HTTP方法
            path: 请求路径
            timestamp: 时间戳
            body: 请求体
            api_key: API密钥（用于选择对应的签名密钥）

        Returns:
            str: 签名字符串
        """
        # 根据API密钥选择对应的签名密钥
        signature_secret = self.signature_secrets.get(api_key, self.signature_secrets['default'])

        # 构造签名字符串
        sign_string = f"{method}\n{path}\n{timestamp}\n{body}"

        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            signature_secret.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature
    
    def verify_signature(self, request: Request, body: str = "", api_key: str = None) -> Tuple[bool, str]:
        """
        验证请求签名

        Args:
            request: FastAPI请求对象
            body: 请求体
            api_key: API密钥（用于选择对应的签名密钥）

        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        if not self.enable_signature:
            return True, ""

        # 获取签名相关头部
        timestamp = request.headers.get("X-Timestamp")
        signature = request.headers.get("X-Signature")

        if not timestamp or not signature:
            return False, "缺少签名头部信息"

        try:
            # 检查时间戳有效性
            current_time = int(time.time())
            request_time = int(timestamp)

            if abs(current_time - request_time) > self.signature_timeout:
                return False, "请求时间戳已过期"

            # 生成期望的签名
            expected_signature = self.generate_signature(
                method=request.method,
                path=request.url.path,
                timestamp=timestamp,
                body=body,
                api_key=api_key
            )

            # 验证签名
            if not hmac.compare_digest(signature, expected_signature):
                return False, "签名验证失败"

            return True, ""

        except (ValueError, TypeError) as e:
            return False, f"签名验证错误: {str(e)}"
    
    async def authenticate_request(self, request: Request, body: str = "") -> Tuple[bool, str, Optional[str]]:
        """
        认证请求
        
        Args:
            request: FastAPI请求对象
            body: 请求体
            
        Returns:
            tuple[bool, str, Optional[str]]: (是否通过, 错误信息, 用户名)
        """
        # 验证API密钥
        api_key = request.headers.get("X-API-Key")
        is_valid_key, username = self.validate_api_key(api_key)
        
        if not is_valid_key:
            logger.warning(f"API密钥验证失败: {api_key} from IP: {request.client.host}")
            return False, "无效的API密钥", None
        
        # 验证请求签名（传入API密钥以选择对应的签名密钥）
        is_valid_signature, signature_error = self.verify_signature(request, body, api_key)

        if not is_valid_signature:
            logger.warning(f"请求签名验证失败: {signature_error} from IP: {request.client.host}")
            return False, signature_error, None
        
        logger.info(f"API认证成功: 用户 {username} from IP: {request.client.host}")
        return True, "", username

class LinkEncryption:
    """链接加密器 - 使用AES-256-GCM加密"""

    def __init__(self):
        self.enable_encryption = security_config.ENABLE_LINK_ENCRYPTION
        # 确保密钥长度为32字节（AES-256）
        key_material = security_config.LINK_ENCRYPTION_KEY.encode('utf-8')
        self.encryption_key = hashlib.sha256(key_material).digest()[:32]

        # 导入加密库
        try:
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.primitives.ciphers.aead import AESGCM
            from cryptography.hazmat.backends import default_backend
            import os

            self.Cipher = Cipher
            self.algorithms = algorithms
            self.modes = modes
            self.AESGCM = AESGCM
            self.default_backend = default_backend
            self.os = os
            self.crypto_available = True

        except ImportError:
            logger.warning("cryptography库未安装，使用备用XOR加密（不安全）")
            self.crypto_available = False

    def _aes_encrypt(self, data: str) -> str:
        """
        使用AES-256-GCM加密数据

        Args:
            data: 要加密的数据

        Returns:
            str: 加密后的数据（包含nonce）
        """
        try:
            # 生成随机nonce（12字节用于GCM）
            nonce = self.os.urandom(12)

            # 创建AESGCM实例
            aesgcm = self.AESGCM(self.encryption_key)

            # 加密数据
            ciphertext = aesgcm.encrypt(nonce, data.encode('utf-8'), None)

            # 组合nonce和密文
            encrypted_data = nonce + ciphertext

            # Base64编码
            encoded = base64.b64encode(encrypted_data).decode('utf-8')

            return encoded

        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            # 降级到XOR加密
            return self._xor_encrypt_decrypt(data, self.encryption_key[:16])

    def _aes_decrypt(self, encrypted_data: str) -> str:
        """
        使用AES-256-GCM解密数据

        Args:
            encrypted_data: 加密的数据

        Returns:
            str: 解密后的数据
        """
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))

            # 分离nonce和密文
            nonce = encrypted_bytes[:12]
            ciphertext = encrypted_bytes[12:]

            # 创建AESGCM实例
            aesgcm = self.AESGCM(self.encryption_key)

            # 解密数据
            plaintext = aesgcm.decrypt(nonce, ciphertext, None)

            return plaintext.decode('utf-8')

        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            # 尝试XOR解密（向后兼容）
            try:
                encrypted = base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
                return self._xor_encrypt_decrypt(encrypted, self.encryption_key[:16])
            except:
                raise e

    def _xor_encrypt_decrypt(self, data: str, key: bytes) -> str:
        """
        备用XOR加密/解密（不安全，仅用于向后兼容）

        Args:
            data: 要加密/解密的数据
            key: 密钥

        Returns:
            str: 加密/解密后的数据
        """
        result = []
        key_len = len(key)

        for i, char in enumerate(data):
            result.append(chr(ord(char) ^ key[i % key_len]))

        return ''.join(result)

    def encrypt_link(self, link: str) -> str:
        """
        加密链接

        Args:
            link: 原始链接

        Returns:
            str: 加密后的链接
        """
        if not self.enable_encryption:
            return link

        try:
            if self.crypto_available:
                # 使用AES-256-GCM加密
                encrypted = self._aes_encrypt(link)
                return f"aes:{encrypted}"
            else:
                # 降级到XOR加密
                encrypted = self._xor_encrypt_decrypt(link, self.encryption_key[:16])
                encoded = base64.b64encode(encrypted.encode('utf-8')).decode('utf-8')
                return f"xor:{encoded}"

        except Exception as e:
            logger.error(f"链接加密失败: {e}")
            return link

    def decrypt_link(self, encrypted_link: str) -> str:
        """
        解密链接

        Args:
            encrypted_link: 加密的链接

        Returns:
            str: 解密后的链接
        """
        if not self.enable_encryption:
            return encrypted_link

        try:
            if encrypted_link.startswith("aes:"):
                # AES解密
                encrypted_data = encrypted_link[4:]  # 移除 "aes:" 前缀
                return self._aes_decrypt(encrypted_data)

            elif encrypted_link.startswith("xor:"):
                # XOR解密（向后兼容）
                encoded = encrypted_link[4:]  # 移除 "xor:" 前缀
                encrypted = base64.b64decode(encoded.encode('utf-8')).decode('utf-8')
                return self._xor_encrypt_decrypt(encrypted, self.encryption_key[:16])

            elif encrypted_link.startswith("encrypted:"):
                # 旧格式兼容
                encoded = encrypted_link[10:]  # 移除 "encrypted:" 前缀
                encrypted = base64.b64decode(encoded.encode('utf-8')).decode('utf-8')
                return self._xor_encrypt_decrypt(encrypted, self.encryption_key[:16])

            return encrypted_link

        except Exception as e:
            logger.error(f"链接解密失败: {e}")
            return encrypted_link
    
    def encrypt_links_in_result(self, result: dict) -> dict:
        """
        加密结果中的所有链接

        Args:
            result: 搜索结果

        Returns:
            dict: 加密链接后的结果
        """
        if not self.enable_encryption:
            return result

        # 深拷贝结果以避免修改原始数据
        import copy
        encrypted_result = copy.deepcopy(result)

        # 加密WPS搜索结果中links字段的URL
        if 'links' in encrypted_result:
            for link in encrypted_result['links']:
                if 'url' in link:
                    link['url'] = self.encrypt_link(link['url'])

        # 加密CloudSave搜索结果中的link字段
        if 'link' in encrypted_result:
            encrypted_result['link'] = self.encrypt_link(encrypted_result['link'])

        return encrypted_result

# 创建全局实例
api_authenticator = APIAuthenticator()
link_encryptor = LinkEncryption()
