#!/bin/bash

# 一键部署脚本
# 完整的VPS部署流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "移动端电影搜索应用 - 一键部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项] <域名> [邮箱]"
    echo ""
    echo "选项:"
    echo "  -e, --environment    部署环境 (production|staging) [默认: production]"
    echo "  -p, --port          应用端口 [默认: 8506]"
    echo "  -w, --workers       工作进程数 [默认: 2]"
    echo "  -s, --skip-ssl      跳过SSL配置"
    echo "  -h, --help          显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 yourdomain.com <EMAIL>"
    echo "  $0 -e staging -p 8507 staging.yourdomain.com"
    echo "  $0 --skip-ssl test.yourdomain.com"
    echo ""
}

# 默认配置
ENVIRONMENT="production"
PORT="8506"
WORKERS="2"
SKIP_SSL=false
DOMAIN=""
EMAIL=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        -s|--skip-ssl)
            SKIP_SSL=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
        *)
            if [ -z "$DOMAIN" ]; then
                DOMAIN="$1"
            elif [ -z "$EMAIL" ]; then
                EMAIL="$1"
            else
                log_error "过多的参数: $1"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [ -z "$DOMAIN" ]; then
    log_error "请提供域名参数"
    show_usage
    exit 1
fi

if [ -z "$EMAIL" ]; then
    EMAIL="admin@$DOMAIN"
fi

# 验证环境参数
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    log_error "环境参数错误，只支持 production 或 staging"
    exit 1
fi

# 显示配置信息
show_config() {
    log_info "部署配置信息："
    echo "  环境: $ENVIRONMENT"
    echo "  域名: $DOMAIN"
    echo "  邮箱: $EMAIL"
    echo "  端口: $PORT"
    echo "  工作进程: $WORKERS"
    echo "  跳过SSL: $SKIP_SSL"
    echo ""
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查是否为root用户
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要使用root用户运行此脚本"
        log_info "建议创建普通用户并添加sudo权限"
        exit 1
    fi
    
    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_error "当前用户没有sudo权限"
        exit 1
    fi
    
    # 检查操作系统
    if [ ! -f /etc/os-release ]; then
        log_error "不支持的操作系统"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" && "$ID" != "debian" ]]; then
        log_warning "此脚本主要针对Ubuntu/Debian系统测试，其他系统可能需要调整"
    fi
    
    log_success "系统检查通过"
}

# 确认部署
confirm_deployment() {
    log_warning "即将开始部署，这将："
    echo "  1. 安装系统依赖包"
    echo "  2. 创建应用用户和目录"
    echo "  3. 配置Python虚拟环境"
    echo "  4. 配置系统服务"
    echo "  5. 配置Nginx反向代理"
    if [ "$SKIP_SSL" = false ]; then
        echo "  6. 配置SSL证书"
    fi
    echo ""
    
    read -p "确认继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 执行部署步骤
run_deployment() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    log_info "开始部署流程..."
    
    # 步骤1: VPS基础部署
    log_info "=== 步骤1: VPS基础部署 ==="
    if [ -f "$script_dir/deploy_vps.sh" ]; then
        bash "$script_dir/deploy_vps.sh" "$ENVIRONMENT"
    else
        log_error "找不到 deploy_vps.sh 脚本"
        exit 1
    fi
    
    # 步骤2: 配置系统服务
    log_info "=== 步骤2: 配置系统服务 ==="
    if [ -f "$script_dir/setup_service.sh" ]; then
        bash "$script_dir/setup_service.sh"
    else
        log_error "找不到 setup_service.sh 脚本"
        exit 1
    fi
    
    # 步骤3: 配置Nginx
    log_info "=== 步骤3: 配置Nginx ==="
    if [ -f "$script_dir/setup_nginx.sh" ]; then
        bash "$script_dir/setup_nginx.sh" "$DOMAIN" "$PORT"
    else
        log_error "找不到 setup_nginx.sh 脚本"
        exit 1
    fi
    
    # 步骤4: 配置SSL（如果不跳过）
    if [ "$SKIP_SSL" = false ]; then
        log_info "=== 步骤4: 配置SSL证书 ==="
        if [ -f "$script_dir/setup_ssl.sh" ]; then
            bash "$script_dir/setup_ssl.sh" "$DOMAIN" "$EMAIL"
        else
            log_error "找不到 setup_ssl.sh 脚本"
            exit 1
        fi
    else
        log_warning "跳过SSL配置，应用将只支持HTTP访问"
    fi
}

# 部署后检查
post_deployment_check() {
    log_info "=== 部署后检查 ==="
    
    # 检查服务状态
    if sudo systemctl is-active --quiet movie-search; then
        log_success "应用服务运行正常"
    else
        log_error "应用服务未运行"
        sudo systemctl status movie-search --no-pager -l
    fi
    
    # 检查Nginx状态
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务未运行"
        sudo systemctl status nginx --no-pager -l
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":$PORT "; then
        log_success "应用端口 $PORT 监听正常"
    else
        log_warning "应用端口 $PORT 未监听"
    fi
    
    # 检查HTTP访问
    sleep 5
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost:$PORT/health" | grep -q "200"; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败"
    fi
}

# 显示部署结果
show_deployment_result() {
    log_success "部署完成！"
    echo ""
    echo "=== 访问信息 ==="
    if [ "$SKIP_SSL" = false ]; then
        echo "  HTTPS: https://$DOMAIN"
        echo "  HTTPS: https://www.$DOMAIN"
    else
        echo "  HTTP: http://$DOMAIN"
    fi
    echo "  健康检查: http://$DOMAIN/health"
    echo "  API文档: http://$DOMAIN/docs"
    echo "  监控面板: http://$DOMAIN/static/monitoring.html"
    echo ""
    echo "=== 管理命令 ==="
    echo "  查看服务状态: sudo systemctl status movie-search"
    echo "  查看服务日志: sudo journalctl -u movie-search -f"
    echo "  重启服务: sudo systemctl restart movie-search"
    echo "  重启Nginx: sudo systemctl restart nginx"
    echo ""
    echo "=== 文件位置 ==="
    echo "  应用目录: /opt/movie-search"
    echo "  日志目录: /opt/movie-search/logs"
    echo "  配置文件: /opt/movie-search/.env"
    echo "  Nginx配置: /etc/nginx/sites-available/movie-search"
    echo ""
    if [ "$SKIP_SSL" = false ]; then
        echo "=== SSL证书 ==="
        echo "  证书位置: /etc/letsencrypt/live/$DOMAIN/"
        echo "  自动续期: 已配置（每日检查）"
        echo ""
    fi
    echo "=== 安全建议 ==="
    echo "  1. 修改 /opt/movie-search/.env 中的密钥"
    echo "  2. 配置防火墙规则"
    echo "  3. 定期更新系统和依赖"
    echo "  4. 监控应用日志"
    echo ""
}

# 主函数
main() {
    show_config
    check_system
    confirm_deployment
    run_deployment
    post_deployment_check
    show_deployment_result
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
