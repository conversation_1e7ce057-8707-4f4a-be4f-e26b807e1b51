#!/usr/bin/env python3
"""
测试CloudSave链接点击功能
"""

import requests
import json
import time
import hashlib

def generate_browser_fingerprint():
    """生成简单的浏览器指纹"""
    fingerprint_data = "test_browser_fingerprint_12345"
    return hashlib.md5(fingerprint_data.encode()).hexdigest()

def get_session_token():
    """获取会话令牌"""
    fingerprint = generate_browser_fingerprint()
    
    try:
        response = requests.post(
            "http://localhost:8080/api/session",
            headers={"Content-Type": "application/json"},
            json={
                "fingerprint": fingerprint,
                "timestamp": int(time.time() * 1000)
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("token"), fingerprint
        else:
            print(f"获取会话令牌失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"获取会话令牌异常: {e}")
        return None, None

def test_cloudsave_link_workflow():
    """测试CloudSave链接完整工作流程"""
    
    print("🔗 测试CloudSave链接点击工作流程")
    print("=" * 50)
    
    # 获取会话令牌
    token, fingerprint = get_session_token()
    if not token:
        print("❌ 无法获取会话令牌，测试终止")
        return
    
    print(f"✅ 获取会话令牌成功: {token[:16]}...")
    
    # 步骤1: 搜索CloudSave
    print(f"\n📋 步骤1: 执行CloudSave搜索")
    try:
        search_response = requests.post(
            "http://localhost:8080/api/search-cloudsave",
            headers={
                "Content-Type": "application/json",
                "X-Session-Token": token,
                "X-Browser-Fingerprint": fingerprint,
                "X-Timestamp": str(int(time.time() * 1000))
            },
            json={
                "keyword": "测试电影"
            }
        )
        
        if search_response.status_code != 200:
            print(f"❌ CloudSave搜索失败: {search_response.status_code}")
            return
        
        search_data = search_response.json()
        print(f"✅ CloudSave搜索成功，找到 {search_data.get('total_results', 0)} 个结果")
        
        # 获取第一个加密链接
        cloudsave_data = search_data.get('data', {})
        encrypted_link = None
        
        for cloud_type, items in cloudsave_data.items():
            if items:
                encrypted_link = items[0].get('link')
                print(f"📁 从 {cloud_type} 获取测试链接")
                break
        
        if not encrypted_link:
            print("❌ 没有找到可测试的链接")
            return
        
        print(f"🔐 加密链接: {encrypted_link[:50]}...")
        
    except Exception as e:
        print(f"❌ CloudSave搜索异常: {e}")
        return
    
    # 步骤2: 解密链接
    print(f"\n🔓 步骤2: 解密链接")
    try:
        decrypt_response = requests.post(
            "http://localhost:8080/api/decrypt-link",
            headers={
                "Content-Type": "application/json",
                "X-Session-Token": token,
                "X-Browser-Fingerprint": fingerprint,
                "X-Timestamp": str(int(time.time() * 1000))
            },
            json={
                "encrypted_link": encrypted_link
            }
        )
        
        if decrypt_response.status_code == 200:
            decrypt_data = decrypt_response.json()
            decrypted_link = decrypt_data.get('decrypted_link')
            
            print(f"✅ 链接解密成功!")
            print(f"🔗 原始链接: {decrypted_link}")
            
            # 验证链接格式
            if decrypted_link.startswith('https://pan.quark.cn') or decrypted_link.startswith('https://pan.baidu.com'):
                print(f"✅ 链接格式正确，可以正常打开")
            else:
                print(f"⚠️ 链接格式异常: {decrypted_link}")
                
        else:
            print(f"❌ 链接解密失败: {decrypt_response.status_code} - {decrypt_response.text}")
            return
            
    except Exception as e:
        print(f"❌ 链接解密异常: {e}")
        return
    
    # 步骤3: 模拟前端工作流程
    print(f"\n🖥️ 步骤3: 前端工作流程验证")
    print(f"✅ 用户点击'打开链接'按钮")
    print(f"✅ 前端调用 app.handleLinkClick('{encrypted_link[:30]}...', event)")
    print(f"✅ 前端调用 app.decryptLink() 解密链接")
    print(f"✅ 前端调用 window.open('{decrypted_link}', '_blank')")
    print(f"✅ 浏览器打开真实链接")
    
    print(f"\n🎉 CloudSave链接点击工作流程测试完成!")
    print(f"📝 前端用户可以正常点击链接并跳转到网盘页面")

if __name__ == "__main__":
    test_cloudsave_link_workflow()
