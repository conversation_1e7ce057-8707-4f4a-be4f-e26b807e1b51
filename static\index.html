<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>电影搜索 - 移动端</title>
    <meta name="description" content="基于WPS内容搜索和猫眼热门数据的电影资源搜索应用">
    <meta name="keywords" content="电影搜索,网盘资源,猫眼热门,移动端">
    <meta name="theme-color" content="#3B82F6">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body class="bg-gray-50 text-gray-900 antialiased">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-primary text-white shadow-md">
            <div class="container mx-auto px-4 py-4 flex justify-between items-center">
                <div class="flex items-center">
                    <span class="text-2xl mr-2">🎬</span>
                    <span class="text-xl font-bold">电影搜索</span>
                </div>
                <div class="text-sm opacity-90">
                    发现热门影视资源
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow">
            <div class="container mx-auto px-4 py-4" style="max-width: 1200px; margin: 0 auto;">
                <div class="max-w-2xl mx-auto" style="max-width: 42rem; margin: 0 auto; width: 100%;">
                    
                    <!-- 热门推荐区域 -->
                    <div class="mb-6">
                        <div class="mb-3">
                            <h2 class="text-lg font-medium text-gray-700">🔥 热门推荐</h2>
                            <span id="hot-update-time" class="text-xs text-gray-500"></span>
                        </div>

                        <div id="hot-movies" class="flex flex-wrap gap-2">
                            <div class="py-2 text-sm text-gray-500">正在加载热门电影...</div>
                        </div>
                    </div>
                    
                    <!-- 搜索卡片 -->
                    <div class="card p-6 mb-6">
                        <form id="search-form" class="space-y-4">
                            <div>
                                <label for="search-input" class="block text-sm font-medium text-gray-700 mb-1">搜索关键词</label>
                                <div class="relative">
                                    <input 
                                        id="search-input" 
                                        type="text" 
                                        class="form-input pr-12" 
                                        placeholder="输入影视剧名称..." 
                                        maxlength="50" 
                                        autocomplete="off"
                                        required
                                    />
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span id="input-counter" class="text-xs text-gray-400">0/50</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 搜索提示文字 -->
                            <div class="search-hint">
                                搜索一更新快、质量高，搜索二更新慢、资源多
                            </div>

                            <div class="search-buttons-container">
                                <button type="button" class="search-btn active" id="search-btn-1" data-search-type="wps">
                                    <span class="btn-text">搜索一</span>
                                    <span class="btn-icon ml-2">🔍</span>
                                </button>
                                <button type="button" class="search-btn" id="search-btn-2" data-search-type="cloudsave">
                                    <span class="btn-text">搜索二</span>
                                    <span class="btn-icon ml-2">☁️</span>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 搜索结果区域 -->
                    <div id="results-section" class="hidden">
                        <div class="mb-4">
                            <h2 class="text-xl font-semibold">
                                搜索结果
                                <span id="results-count" class="text-sm text-gray-500 font-normal ml-2"></span>
                            </h2>
                            <div id="search-stats" class="text-sm text-gray-500 mt-1"></div>
                        </div>
                        
                        <div id="search-results" class="space-y-4">
                            <!-- 搜索结果将在这里显示 -->
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div id="empty-state" class="hidden mt-8 text-center text-gray-600">
                        <div class="text-4xl mb-4">🔍</div>
                        <p>未找到相关资源</p>
                        <p class="text-sm text-gray-500 mt-2">尝试使用不同的关键词搜索</p>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Footer -->
        <footer class="bg-gray-800 text-white py-4 mt-auto">
            <div class="container mx-auto px-4 text-center text-sm">
                <p>&copy; 2025 电影搜索应用 | 数据来源：猫眼电影</p>
            </div>
        </footer>
    </div>
    
    <!-- 加载指示器 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex flex-col items-center">
            <div class="spinner mb-4"></div>
            <p class="text-gray-700">搜索中，请稍候...</p>
        </div>
    </div>
    
    <!-- 通知提示 -->
    <div id="notification" class="fixed top-4 right-4 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 p-4 z-50 hidden transform transition-all duration-300 ease-in-out">
        <div class="flex items-center">
            <div class="flex-shrink-0 mr-3">
                <div id="notification-icon-success" class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center hidden">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div id="notification-icon-warning" class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center hidden">
                    <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div id="notification-icon-error" class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center hidden">
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <p id="notification-message" class="text-sm font-medium text-gray-900 truncate"></p>
            </div>
            <div class="ml-3 flex-shrink-0">
                <button id="notification-close" class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150">
                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <script src="/static/script.js?v=20250726001"></script>
</body>
</html>